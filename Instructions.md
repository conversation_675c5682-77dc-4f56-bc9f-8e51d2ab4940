# Refactoring File Upload to Use Replit Object Storage

## Current Issues
- Local file storage approach causing server crashes
- 502 Bad Gateway errors during upload process
- Excessive memory usage even for small files

## Approach
We'll switch from local file storage to Replit's Object Storage, which is designed specifically for this use case and should provide better reliability.

## Implementation Plan

1. **Install Required Package**
   - Install `@replit/object-storage` package

2. **Create Object Storage Service**
   - Create a new file `server/objectStorage.ts` to handle all storage operations
   - Implement functions for:
     - Uploading files to Object Storage
     - Retrieving files from Object Storage
     - Generating public URLs for files

3. **Update Database Schema**
   - Keep existing schema as is, but ensure the `fileUrl` field stores Object Storage URLs

4. **Refactor Upload Handler**
   - Remove local file storage code (express-fileupload/multer/busboy)
   - Create a new upload handler that:
     - Processes the uploaded file
     - Uploads to Object Storage
     - Stores metadata in PostgreSQL

5. **Refactor File Serving**
   - Update file serving endpoint to:
     - Either redirect to Object Storage URL
     - Or proxy the file from Object Storage

6. **Update Client Code**
   - Ensure client code can handle the new URL format
   - Keep upload interface the same for consistency

7. **Testing**
   - Test with different file sizes
   - Verify database records
   - Verify file accessibility

## File Changes

1. **New Files**
   - `server/objectStorage.ts`

2. **Modified Files**
   - `server/routes.ts`
   - `server/index.ts` (remove file upload middleware)
   - `server/storage.ts` (minor updates)

3. **Files to Remove**
   - `server/minimal-document.ts`
   - `server/simple-fileupload.ts`
   - `server/bare-minimum-upload.ts`
   - `server/ultra-simple-upload.ts`