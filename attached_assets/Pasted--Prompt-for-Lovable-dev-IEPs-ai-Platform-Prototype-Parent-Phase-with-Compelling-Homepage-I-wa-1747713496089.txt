# Prompt for Lovable.dev: IEPs.ai Platform Prototype - Parent Phase with Compelling Homepage

I want to build a web application called IEPs.ai to help parents navigate the complex world of Individualized Education Plans (IEPs). The platform will use AI to analyze IEP documents and provide guidance to users. We're building this in phases, starting with the parent experience first, and will add teacher and advocate features in future phases.

## Compelling Homepage Design

The homepage should create an immediate emotional connection with parents who are struggling with the IEP process. It needs to convey empathy, understanding, and hope while clearly communicating the platform's value.

### Homepage Elements
- **Hero Section**: Warm, supportive imagery with a headline that speaks directly to parents feeling overwhelmed by IEPs. Include a concise subheading explaining how IEPs.ai helps, with a prominent sign-up button.

- **Personal Story**: A brief section sharing my own experience as a parent navigating the IEP system - how overwhelming it was, the confusion I felt, and why I created this tool to help others.

- **"You're Not Alone" Section**: Statistics or testimonials showing how many families navigate IEPs and struggle with similar challenges.

- **Key Benefits**: Visual explanation of 3-4 core benefits (document organization, AI assistance, simplified understanding, peace of mind) with simple icons and brief descriptions.

- **How It Works**: Step-by-step visual explanation showing the user journey from upload to insights.

- **Testimonials**: (Can use placeholder testimonials for the prototype) from parents about how the platform has helped them feel more confident and prepared.

- **FAQ Section**: Addressing common questions about security, privacy, and how the AI works.

- **Call to Action**: Empathetic final CTA emphasizing empowerment ("Take control of your child's education journey") with sign-up button.

### Design & Tone Requirements
- **Visual Style**: Warm, nurturing imagery balanced with professional credibility
- **Color Scheme**: Calming blues and greens with warm accent colors
- **Copy Tone**: Empathetic, supportive, and encouraging - avoid technical jargon
- **Messaging**: Focus on emotional benefits (confidence, clarity, control) alongside practical features
- **Accessibility**: Ensure high contrast, readable fonts, and clear navigation

## Core Features for Parents (Phase 1)

### Authentication & Account Management
- Parents can sign up with email, Google, or Facebook
- Ability to create a family workspace
- Invite spouse/partner to join the workspace with shared access
- Simple user profile management

### Child Management
- Add multiple children to the workspace
- Each child has their own dedicated section/profile
- Child dashboard showing documents, insights, and conversation history
- Easily switch between children in the same workspace

### Document Management
- Upload and store IEP documents for each child
- Document organization by school year and document type
- Document viewer for IEPs and related materials
- Automatic document anonymization for privacy protection
- Document version history

### AI Assistant
- Conversational AI interface to ask questions about a child's IEP
- AI analysis providing insights from uploaded documents
- Saved conversation history for future reference
- Suggested questions based on document content
- Summary of key IEP components and goals

### Dashboard & Navigation
- Family workspace overview showing all children
- Per-child dashboard with document library, insights, and chat
- Simple navigation between features
- Activity feed showing recent uploads and AI interactions

## Technical Requirements

1. **Authentication System**
   - Multiple sign-in methods (email, Google, Facebook)
   - Secure invitation system for spouse/partner
   - Role-based permissions within the family workspace

2. **Document Storage**
   - Secure file storage (S3 buckets or equivalent)
   - PDF/document viewing capabilities
   - Document organization system

3. **AI Integration**
   - Integration with AI service for document analysis
   - Conversational interface for the AI assistant
   - Storage for conversation history

4. **User Interface**
   - Clean, accessible design with a trustworthy feel
   - Mobile-responsive interface
   - Intuitive navigation between children and features
   - Simple document upload process

## User Flows to Prototype
1. Homepage to signup flow (capturing the emotional journey)
2. Parent signup and account creation
3. Adding a child to the workspace
4. Uploading an IEP document for a child
5. Using the AI assistant to ask questions about the IEP
6. Inviting a spouse/partner to the workspace
7. Switching between multiple children in the workspace

The prototype should first demonstrate how the homepage creates an emotional connection with overwhelmed parents, then show how parents can easily manage IEP documents for multiple children and gain insights through the AI assistant, all within a shared family workspace. This phase focuses exclusively on the parent experience, with teacher and advocate functionality planned for future development.