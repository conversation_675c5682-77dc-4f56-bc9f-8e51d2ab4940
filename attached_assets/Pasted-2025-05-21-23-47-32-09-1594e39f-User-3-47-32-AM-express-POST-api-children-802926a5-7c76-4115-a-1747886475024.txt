2025-05-21 23:47:32.09
1594e39f
User
3:47:32 AM [express] POST /api/children/802926a5-7c76-4115-a35b-5d4b83bb60ff/documents/upload 500 in…
2025-05-21 23:49:44.23
c9c48a8b
System
system: received signal terminated
2025-05-21 23:56:33.00
3b84b14d
System
starting up user application
2025-05-21 23:56:33.48
3b84b14d
System
forwarding local port 5000 to external port 80 (mapped as 1104)
2025-05-21 23:56:36.49
3b84b14d
User
> rest-express@1.0.0 start
2025-05-21 23:56:36.49
3b84b14d
User
> NODE_ENV=production node dist/index.js
2025-05-21 23:56:39.93
3b84b14d
User
[DEBUG] Initializing database connection with URL: postgresql://neondb_...
2025-05-21 23:56:40.31
3b84b14d
User
3:56:40 AM [express] serving on port 5000
2025-05-21 23:56:40.77
3b84b14d
User
[DEBUG] Database connection successful: { now: 2025-05-22T03:56:40.746Z }
2025-05-21 23:56:51.72
3b84b14d
User
Auth check - authenticated: true
2025-05-21 23:56:51.72
3b84b14d
User
Fetching user data for: 108109139812153154035
2025-05-21 23:56:51.74
3b84b14d
User
User authenticated successfully: 108109139812153154035
2025-05-21 23:56:51.77
3b84b14d
User
3:56:51 AM [express] GET /api/auth/user 304 in 108ms :: {"id":"108109139812153154035","email":"chaml…
2025-05-21 23:56:51.95
3b84b14d
User
[DEBUG] GET /api/children - Auth user: {
2025-05-21 23:56:51.95
3b84b14d
User
userId: '108109139812153154035',
2025-05-21 23:56:51.95
3b84b14d
User
userObject: '{"id":"108109139812153154035","email":"<EMAIL>","firstName":"Vatche","lastName":"Chamlian","profileImageUrl":"https://lh3.googleusercontent.com/a/ACg8ocIWMgMd2GTkkzsJS-MNOm6-tJUgw-EhjG1hcXLPA3yQJEqXFw=s96-c","createdAt":"2025-05-21T05:31:02.604Z","updatedAt":"2025-05-21T22:19:01.536Z"}'
2025-05-21 23:56:51.95
3b84b14d
User
}
2025-05-21 23:56:51.98
3b84b14d
User
[DEBUG] Found children: [{"id":"802926a5-7c76-4115-a35b-5d4b83bb60ff","name":"Anna","birthMonth":"September","birthYear":"2012","avatarUrl":null,"createdBy":"108109139812153154035","createdAt":"2025-05-21T06:50:46.860Z","updatedAt":"2025-05-21T06:50:46.860Z"}]
2025-05-21 23:56:52.01
3b84b14d
User
3:56:52 AM [express] GET /api/children 304 in 100ms :: [{"id":"802926a5-7c76-4115-a35b-5d4b83bb60ff"…
2025-05-21 23:56:52.12
3b84b14d
User
3:56:52 AM [express] GET /api/family-members 304 in 179ms :: []
2025-05-21 23:56:56.63
3b84b14d
User
3:56:56 AM [express] GET /api/children/802926a5-7c76-4115-a35b-5d4b83bb60ff 304 in 121ms :: {"id":"8…
2025-05-21 23:57:02.43
3b84b14d
User
Processing file upload: luma-certificate.pdf (114791 bytes)
2025-05-21 23:57:02.43
3b84b14d
User
Uploading to Object Storage: iep-documents/1747886222438-ca4fde79.pdf
2025-05-21 23:57:02.72
3b84b14d
User
File uploaded successfully to Object Storage
2025-05-21 23:57:02.72
3b84b14d
User
Storing document metadata in database for child 802926a5-7c76-4115-a35b-5d4b83bb60ff
2025-05-21 23:57:02.77
3b84b14d
User
Document saved with ID: 0164bf56-7d82-45ac-b627-22f2a8ff00dd
2025-05-21 23:57:02.79
3b84b14d
User
3:57:02 AM [express] POST /api/children/802926a5-7c76-4115-a35b-5d4b83bb60ff/documents/upload 201 in…
2025-05-21 23:57:02.90
3b84b14d
User
[DEBUG] GET /api/children - Auth user: {
2025-05-21 23:57:02.90
3b84b14d
User
userId: '108109139812153154035',
2025-05-21 23:57:02.90
3b84b14d
User
userObject: '{"id":"108109139812153154035","email":"<EMAIL>","firstName":"Vatche","lastName":"Chamlian","profileImageUrl":"https://lh3.googleusercontent.com/a/ACg8ocIWMgMd2GTkkzsJS-MNOm6-tJUgw-EhjG1hcXLPA3yQJEqXFw=s96-c","createdAt":"2025-05-21T05:31:02.604Z","updatedAt":"2025-05-21T22:19:01.536Z"}'
2025-05-21 23:57:02.90
3b84b14d
User
}
2025-05-21 23:57:02.92
3b84b14d
User
[DEBUG] Found children: [{"id":"802926a5-7c76-4115-a35b-5d4b83bb60ff","name":"Anna","birthMonth":"September","birthYear":"2012","avatarUrl":null,"createdBy":"108109139812153154035","createdAt":"2025-05-21T06:50:46.860Z","updatedAt":"2025-05-21T06:50:46.860Z"}]
2025-05-21 23:57:02.94
3b84b14d
User
3:57:02 AM [express] GET /api/children 304 in 92ms :: [{"id":"802926a5-7c76-4115-a35b-5d4b83bb60ff",…