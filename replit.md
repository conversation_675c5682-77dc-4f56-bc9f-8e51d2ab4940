# IEPs.ai - Replit Configuration

## Overview

IEPs.ai is a web-based platform designed to help parents understand and navigate their children's Individualized Education Plans (IEPs) through AI-powered document analysis and conversation. The application is currently in beta and focuses on empowering parents to become confident advocates for their children's educational needs.

## System Architecture

### Technology Stack
- **Frontend**: React with TypeScript, Vite for build tooling
- **Backend**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Google OAuth2 + custom email/password authentication
- **File Storage**: Currently using Replit Object Storage (transitioning from local storage)
- **AI Integration**: OpenAI GPT-4o for document analysis and question answering
- **UI Framework**: Tailwind CSS with shadcn/ui components

### Project Structure
```
├── client/                 # React frontend application
├── server/                 # Express.js backend application
├── shared/                 # Shared types and schemas
├── prompts/               # AI prompt templates
├── migrations/            # Database migrations
└── uploads/               # Temporary local storage (being phased out)
```

## Key Components

### Frontend Architecture
- **React with TypeScript**: Modern functional components with hooks
- **Wouter**: Lightweight routing library
- **TanStack Query**: Server state management and caching
- **shadcn/ui**: Pre-built accessible UI components
- **Tailwind CSS**: Utility-first CSS framework

### Backend Architecture
- **Express.js**: RESTful API server with modular handler structure
- **Drizzle ORM**: Type-safe database operations
- **Passport.js**: Authentication middleware for Google OAuth
- **Custom middleware**: File upload handling with multer

### Database Schema
Key tables include:
- `users`: User authentication and profile data
- `children`: Child profiles linked to users
- `documents`: Uploaded IEP documents with metadata
- `conversations`: AI conversation threads
- `messages`: Individual messages in conversations
- `sessions`: Session management for authentication

### File Storage Strategy
Currently transitioning from local file storage to Replit Object Storage:
- **Current**: Using `@replit/object-storage` package
- **Legacy**: Local file system (causing memory issues)
- **Migration**: Ongoing refactor to improve reliability and scalability

## Data Flow

### Document Upload Process
1. User uploads document via React frontend
2. File processed through multer middleware
3. Document uploaded to Replit Object Storage
4. Metadata stored in PostgreSQL
5. Optional AI analysis triggered for text extraction

### AI Conversation Flow
1. User asks question about child's documents
2. System retrieves relevant documents and extracted text
3. OpenAI API processes question with context
4. Streaming response delivered to frontend
5. Conversation history stored in database

### Authentication Flow
1. User can authenticate via Google OAuth or email/password
2. Session managed with PostgreSQL-backed session store
3. User data synchronized between authentication providers

## External Dependencies

### APIs and Services
- **OpenAI API**: Document analysis and conversational AI
- **Google OAuth2**: Authentication provider
- **Brevo (Sendinblue)**: Transactional email service
- **Google reCAPTCHA v3**: Bot protection

### NPM Packages
- **Core**: express, react, typescript, drizzle-orm
- **Authentication**: passport, express-session
- **File Handling**: multer, @replit/object-storage
- **AI**: openai
- **UI**: @radix-ui components, tailwindcss

## Deployment Strategy

### Replit Configuration
- **Runtime**: Node.js 20 with PostgreSQL 16
- **Build Command**: `npm run build`
- **Start Command**: `npm run start`
- **Development**: `npm run dev` with hot reloading
- **Port**: Application runs on port 5000

### Environment Variables Required
- `DATABASE_URL`: PostgreSQL connection string
- `OPENAI_API_KEY`: OpenAI API access
- `SESSION_SECRET`: Session encryption key
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`: OAuth credentials
- `BREVO_API_KEY`: Email service credentials
- `RECAPTCHA_SECRET_KEY`: reCAPTCHA verification

### Object Storage
- Configured with default bucket for file storage
- Bucket ID: `replit-objstore-e87f6b2a-c595-4775-88f1-ad833c4225e4`

## Changelog

- June 24, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.