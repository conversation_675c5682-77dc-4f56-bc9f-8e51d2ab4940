import { Express, RequestHandler } from 'express';
import passport from 'passport';
import session from 'express-session';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import connectPg from "connect-pg-simple";
import { storage } from "./storage";

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000; // 1 week
  const pgStore = connectPg(session);
  const sessionStore = new pgStore({
    conString: process.env.DATABASE_URL,
    createTableIfMissing: false,
    ttl: sessionTtl,
    tableName: "sessions",
  });
  
  const isProduction = process.env.NODE_ENV === 'production';
  
  return session({
    secret: process.env.SESSION_SECRET || "dev-secret-key",
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      secure: isProduction, // Only use secure in production
      sameSite: isProduction ? 'none' : 'lax', // Use 'none' for cross-site in production
      maxAge: sessionTtl,
    },
  });
}

async function upsertUser(profile: any) {
  try {
    const user = await storage.upsertUser({
      id: profile.id,
      email: profile.emails?.[0]?.value || '',
      firstName: profile.name?.givenName || '',
      lastName: profile.name?.familyName || '',
      profileImageUrl: profile.photos?.[0]?.value || null,
    });
    return user;
  } catch (error) {
    console.error('Error upserting user:', error);
    throw error;
  }
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  
  // Apply session middleware
  const sessionMiddleware = getSession();
  app.use(sessionMiddleware);
  
  // Initialize passport
  app.use(passport.initialize());
  app.use(passport.session());
  
  // Add CORS headers for auth routes specifically
  app.use('/api/auth', (req, res, next) => {
    res.header('Access-Control-Allow-Credentials', 'true');
    next();
  });

  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: string, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  });

  const callbackURL = process.env.NODE_ENV === 'production'
    ? 'https://ieps.ai/api/auth/google/callback'
    : 'http://localhost:5000/api/auth/google/callback';

  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID!,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    callbackURL,
    scope: ['profile', 'email']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('Google auth successful for:', profile.displayName);
      const user = await upsertUser(profile);
      return done(null, user);
    } catch (error) {
      console.error('Error in Google OAuth callback:', error);
      return done(error as Error, undefined);
    }
  }));

  // Google OAuth routes
  app.get('/api/auth/google', (req, res, next) => {
    // Pass through the state parameter if provided
    const state = req.query.state as string;
    const authOptions: any = { scope: ['profile', 'email'] };
    if (state) {
      authOptions.state = state;
    }
    passport.authenticate('google', authOptions)(req, res, next);
  });

  app.get('/api/auth/google/callback', 
    passport.authenticate('google', { failureRedirect: '/' }),
    (req, res) => {
      // Check for state parameter with redirect URL
      const state = req.query.state as string;
      if (state) {
        try {
          const redirectUrl = decodeURIComponent(state);
          console.log('[DEBUG] Google OAuth redirect to:', redirectUrl);
          // Extract just the path and query params
          const url = new URL(redirectUrl);
          const redirectPath = url.pathname + url.search;
          res.redirect(redirectPath);
        } catch (error) {
          console.error('Error parsing state parameter:', error);
          res.redirect('/dashboard');
        }
      } else {
        // Successful authentication, redirect to dashboard
        res.redirect('/dashboard');
      }
    }
  );

  // Add a route to check authentication status
  app.get('/api/auth/status', (req, res) => {
    // Check if passport is properly initialized and isAuthenticated exists
    if (typeof req.isAuthenticated === 'function' && req.isAuthenticated()) {
      res.json({
        authenticated: true,
        user: req.user
      });
    } else {
      res.json({
        authenticated: false,
        message: "User not authenticated"
      });
    }
  });
  
  // Logout route
  app.get('/api/auth/logout', (req, res) => {
    if (typeof req.logout === 'function') {
      req.logout(() => {
        res.redirect('/');
      });
    } else {
      // Fallback if passport isn't properly initialized
      res.redirect('/');
    }
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  // TEMPORARY DEV MODE: Always authenticate for testing
  if (process.env.NODE_ENV === 'development') {
    // Create a mock user for development
    (req as any).user = {
      id: 'test-user-123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      profileImageUrl: null
    };
    console.log("Development mode: Using test user for authentication");
    return next();
  }
  
  // NORMAL AUTH LOGIC FOR PRODUCTION
  // Check if passport is properly initialized and isAuthenticated method exists
  if (typeof req.isAuthenticated !== 'function') {
    console.log("Authentication check failed: Passport not properly initialized");
    return res.status(500).json({ message: "Authentication system error" });
  }
  
  if (!req.isAuthenticated()) {
    console.log("Authentication check failed: User not authenticated");
    return res.status(401).json({ message: "Unauthorized" });
  }
  
  return next();
};