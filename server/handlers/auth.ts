import type { Express } from "express";
import { isAuthenticated } from "../googleAuth";
import { storage as dbStorage } from "../storage";
import {
  hashPassword,
  verifyPassword,
  validatePassword,
  validateEmail,
} from "../services/passwordUtils";
import { 
  sendWelcomeEmail, 
  sendPasswordResetEmail, 
  sendContactFormEmail,
  sendFamilyInvitationEmail,
  sendFeedbackEmail
} from "../services/emailService";
import { verifyRecaptchaToken } from "../services/recaptcha";
import { randomUUID } from "crypto";

/**
 * Register all authentication-related routes
 */
export function authRoutes(app: Express) {
  // Get current user info
  app.get("/api/auth/user", handleGetUser);

  // Email/password authentication
  app.post("/api/auth/register", handleRegister);
  app.post("/api/auth/login", handleLogin);
  app.post("/api/auth/logout", handleLogout);
  
  // Password reset routes
  app.post("/api/auth/forgot-password", handleForgotPassword);
  app.post("/api/auth/reset-password", handleResetPassword);
  
  // Contact form route
  app.post("/api/contact", handleContactForm);
  
  // Feedback route
  app.post("/api/feedback", submitFeedback);

  // Family members routes
  app.get("/api/family-members", isAuthenticated, handleGetFamilyMembers);
  app.post(
    "/api/family-members/invite",
    isAuthenticated,
    handleInviteFamilyMember,
  );

  // Family invitation routes
  app.get("/api/family-invitations", isAuthenticated, handleGetFamilyInvitations);
  app.get("/api/family-invitations/:id", handleGetFamilyInvitation);
  app.post("/api/family-invitations/:id/accept", isAuthenticated, handleAcceptFamilyInvitation);
  app.post("/api/family-invitations/:id/decline", isAuthenticated, handleDeclineFamilyInvitation);

  // User profile routes
  app.patch("/api/user/profile", isAuthenticated, handleUpdateUserProfile);
}

/**
 * Handle user registration with email/password
 */
async function handleRegister(req: any, res: any) {
  try {
    const { email, password, firstName, lastName, recaptchaToken } = req.body;

    // Validate input
    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email and password are required" });
    }

    // Verify reCAPTCHA token
    const recaptchaResult = await verifyRecaptchaToken(
      recaptchaToken,
      'signup',
      0.5 // Minimum score for signup
    );

    if (!recaptchaResult.success) {
      console.log('reCAPTCHA verification failed for signup:', recaptchaResult.error);
      return res.status(400).json({
        message: recaptchaResult.error || 'Security verification failed',
        code: 'RECAPTCHA_VERIFICATION_FAILED'
      });
    }

    console.log(`Registration attempt with reCAPTCHA score: ${recaptchaResult.score}`);

    // Validate email format
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      return res.status(400).json({ message: emailValidation.error });
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        message: "Password does not meet requirements",
        errors: passwordValidation.errors,
      });
    }

    // Check if user already exists
    const existingUser = await dbStorage.getUserByEmail(email);
    if (existingUser) {
      return res
        .status(409)
        .json({ message: "User with this email already exists" });
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const userId = randomUUID();
    const user = await dbStorage.upsertUser({
      id: userId,
      email: email.toLowerCase(),
      firstName: firstName || "",
      lastName: lastName || "",
      passwordHash,
      authProvider: "email",
      emailVerified: false, // In a real app, you'd send a verification email
      profileImageUrl: null,
    });

    // Send welcome email (don't block registration if email fails)
    const userName = `${firstName || ''} ${lastName || ''}`.trim() || 'there';
    sendWelcomeEmail(user.email, userName).catch(error => {
      console.error('Failed to send welcome email:', error);
    });

    // Log the user in by setting up session
    req.login(
      {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      (err: any) => {
        if (err) {
          console.error("Login error after registration:", err);
          return res
            .status(500)
            .json({ message: "Registration successful but login failed" });
        }

        res.status(201).json({
          message: "User registered successfully",
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            profileImageUrl: user.profileImageUrl,
          },
        });
      },
    );
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({ message: "Failed to register user" });
  }
}

/**
 * Handle user login with email/password
 */
async function handleLogin(req: any, res: any) {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email and password are required" });
    }

    // Find user by email
    const user = await dbStorage.getUserByEmail(email.toLowerCase());
    if (!user) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    // Check if user has a password (might be OAuth-only user)
    if (!user.passwordHash) {
      return res.status(401).json({
        message:
          "This account was created with social login. Please use Google to sign in.",
      });
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);
    if (!isPasswordValid) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    // Log the user in
    req.login(
      {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      (err: any) => {
        if (err) {
          console.error("Login error:", err);
          return res.status(500).json({ message: "Login failed" });
        }

        res.json({
          message: "Login successful",
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            profileImageUrl: user.profileImageUrl,
          },
        });
      },
    );
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Failed to login" });
  }
}

/**
 * Handle user logout
 */
async function handleLogout(req: any, res: any) {
  if (typeof req.logout === "function") {
    req.logout((err: any) => {
      if (err) {
        console.error("Logout error:", err);
        return res.status(500).json({ message: "Logout failed" });
      }
      res.json({ message: "Logout successful" });
    });
  } else {
    res.json({ message: "Already logged out" });
  }
}

/**
 * Handle forgot password request
 */
async function handleForgotPassword(req: any, res: any) {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // Find user by email
    const user = await dbStorage.getUserByEmail(email.toLowerCase());
    
    // Always return success to prevent email enumeration attacks
    // But only send email if user actually exists
    if (user && user.passwordHash) {
      // Generate reset token (you might want to store this in DB with expiration)
      const resetToken = randomUUID();
      
      // In a real app, you'd store this token in the database with an expiration time
      // For now, we'll include the user ID in the token (not secure for production!)
      const tokenWithUserId = `${resetToken}-${user.id}`;
      
      const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'there';
      
      // Send password reset email
      sendPasswordResetEmail(user.email, userName, tokenWithUserId).catch(error => {
        console.error('Failed to send password reset email:', error);
      });
    }

    // Always return success message
    res.json({ 
      message: "If an account with that email exists, we've sent a password reset link." 
    });
    
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({ message: "Failed to process password reset request" });
  }
}

/**
 * Handle password reset with token
 */
async function handleResetPassword(req: any, res: any) {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({ message: "Token and new password are required" });
    }

    // Validate new password
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        message: "Password does not meet requirements",
        errors: passwordValidation.errors,
      });
    }

    // Extract user ID from token (this is a simplified approach - use proper token storage in production)
    const tokenParts = token.split('-');
    if (tokenParts.length !== 6) { // UUID has 5 parts, we added user ID as 6th
      return res.status(400).json({ message: "Invalid reset token" });
    }

    const userId = tokenParts[5];
    const user = await dbStorage.getUser(userId);
    
    if (!user) {
      return res.status(400).json({ message: "Invalid reset token" });
    }

    // Hash new password
    const passwordHash = await hashPassword(newPassword);

    // Update user's password
    await dbStorage.upsertUser({
      ...user,
      passwordHash,
    });

    res.json({ message: "Password reset successful" });

  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({ message: "Failed to reset password" });
  }
}

/**
 * Handle contact form submission
 */
async function handleContactForm(req: any, res: any) {
  try {
    const { name, email, subject, message, recaptchaToken } = req.body;

    // Validate input
    if (!name || !email || !subject || !message) {
      return res.status(400).json({ 
        message: "All fields are required" 
      });
    }

    // Verify reCAPTCHA token
    const recaptchaResult = await verifyRecaptchaToken(
      recaptchaToken,
      'contact',
      0.5 // Minimum score for contact form
    );

    if (!recaptchaResult.success) {
      console.log('reCAPTCHA verification failed for contact form:', recaptchaResult.error);
      return res.status(400).json({
        message: recaptchaResult.error || 'Security verification failed',
        code: 'RECAPTCHA_VERIFICATION_FAILED'
      });
    }

    console.log(`Contact form submission with reCAPTCHA score: ${recaptchaResult.score}`);

    // Validate email format
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      return res.status(400).json({ message: emailValidation.error });
    }

    // Send contact form email
    const emailSent = await sendContactFormEmail({
      name,
      email,
      subject,
      message
    });

    if (emailSent) {
      res.json({ 
        message: "Thank you for your message. We'll get back to you soon!" 
      });
    } else {
      res.status(500).json({ 
        message: "Failed to send message. Please try again later." 
      });
    }

  } catch (error) {
    console.error("Contact form error:", error);
    res.status(500).json({ message: "Failed to process contact form" });
  }
}

/**
 * Get current authenticated user
 */
async function handleGetUser(req: any, res: any) {
  console.log(
    "Auth check - authenticated:",
    typeof req.isAuthenticated === "function" ? req.isAuthenticated() : false,
  );

  // Removed development mode override to ensure consistent authentication

  if (typeof req.isAuthenticated !== "function" || !req.isAuthenticated()) {
    console.log("User not authenticated");
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    console.log("Fetching user data for:", userId);
    const user = await dbStorage.getUser(userId);
    if (!user) {
      // If we have a valid session but no user in DB, return the session user
      console.log("User not found in DB, returning session user");
      return res.json(req.user);
    }

    console.log("User authenticated successfully:", userId);
    res.json({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImageUrl: user.profileImageUrl,
    });
  } catch (error) {
    console.error("Error fetching user:", error);
    res.status(500).json({ message: "Failed to fetch user" });
  }
}

/**
 * Get family members for authenticated user
 */
async function handleGetFamilyMembers(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const familyMembers = await dbStorage.getFamilyMembersByUserId(userId);
    res.json(familyMembers);
  } catch (error) {
    console.error("Error fetching family members:", error);
    res.status(500).json({ message: "Failed to fetch family members" });
  }
}

/**
 * Invite a family member
 */
async function handleInviteFamilyMember(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const { email, firstName, lastName } = req.body;

    if (!email || !firstName) {
      return res.status(400).json({ message: "Email and first name are required" });
    }

    // Get the inviter's user info
    const inviter = await dbStorage.getUser(userId);
    if (!inviter) {
      return res.status(404).json({ message: "User not found" });
    }

    // Create the invitation in the database
    const invitation = await dbStorage.createFamilyInvitation(userId, email);

    // Send the invitation email
    const emailSent = await sendFamilyInvitationEmail(
      inviter.email,
      `${inviter.firstName} ${inviter.lastName}`.trim(),
      email,
      `${firstName} ${lastName || ''}`.trim(),
      invitation.id
    );

    if (!emailSent) {
      // If email fails, delete the invitation
      await dbStorage.deleteFamilyInvitation(invitation.id);
      return res.status(500).json({ message: "Failed to send invitation email" });
    }

    res.json({ success: true, invitation });
  } catch (error) {
    console.error("Error inviting family member:", error);
    res.status(500).json({ message: "Failed to invite family member" });
  }
}

/**
 * Update user profile
 */
async function handleUpdateUserProfile(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const { firstName, lastName } = req.body;

    if (!firstName && !lastName) {
      return res.status(400).json({ message: "At least one field is required" });
    }

    // Update user's profile
    const updatedUser = await dbStorage.updateUserProfile(userId, {
      firstName,
      lastName,
    });

    if (!updatedUser) {
      return res.status(500).json({ message: "Failed to update user profile" });
    }

    res.json({
      message: "User profile updated successfully",
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        profileImageUrl: updatedUser.profileImageUrl,
      },
    });
  } catch (error) {
    console.error("Error updating user profile:", error);
    res.status(500).json({ message: "Failed to update user profile" });
  }
}

/**
 * Get family invitations for authenticated user
 */
async function handleGetFamilyInvitations(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    
    console.log("[DEBUG] Getting invitations for user:", userId);
    
    // Get all invitations in the database for debugging
    const { db } = await import("../db");
    const { familyInvitations, familyMembers } = await import("../../shared/schema");
    
    const allInvitations = await db.select().from(familyInvitations);
    console.log("[DEBUG] ALL invitations in database:", allInvitations);
    
    // Get all family members in the database for debugging
    const allFamilyMembers = await db.select().from(familyMembers);
    console.log("[DEBUG] ALL family members in database:", allFamilyMembers);
    
    const invitations = await dbStorage.getFamilyInvitationsByUserId(userId);
    res.json(invitations);
  } catch (error) {
    console.error("Error fetching family invitations:", error);
    res.status(500).json({ message: "Failed to fetch invitations" });
  }
}

/**
 * Get family invitation details
 */
async function handleGetFamilyInvitation(req: any, res: any) {
  try {
    const invitationId = req.params.id;
    
    if (!invitationId) {
      return res.status(400).json({ message: "Invitation ID is required" });
    }

    const invitation = await dbStorage.getFamilyInvitation(invitationId);
    
    if (!invitation) {
      return res.status(404).json({ message: "Invitation not found" });
    }

    res.json(invitation);
  } catch (error) {
    console.error("Error fetching family invitation:", error);
    res.status(500).json({ message: "Failed to fetch invitation" });
  }
}

/**
 * Accept family invitation
 */
async function handleAcceptFamilyInvitation(req: any, res: any) {
  console.log("[DEBUG] ACCEPT INVITATION ROUTE HIT - START");
  console.log("[DEBUG] Request params:", req.params);
  console.log("[DEBUG] Request user:", req.user);
  
  try {
    const invitationId = req.params.id;
    const userId = req.user?.id;
    
    console.log("[DEBUG] Extracted invitationId:", invitationId);
    console.log("[DEBUG] Extracted userId:", userId);
    
    if (!userId) {
      console.log("[DEBUG] ERROR: No user ID found");
      return res.status(401).json({ message: "User not authenticated" });
    }

    if (!invitationId) {
      console.log("[DEBUG] ERROR: No invitation ID found");
      return res.status(400).json({ message: "Invitation ID is required" });
    }

    // Get the invitation
    const invitation = await dbStorage.getFamilyInvitation(invitationId);
    
    if (!invitation) {
      return res.status(404).json({ message: "Invitation not found" });
    }

    if (invitation.status !== 'pending') {
      return res.status(400).json({ message: "Invitation is no longer pending" });
    }

    // Get the current user
    const currentUser = await dbStorage.getUser(userId);
    if (!currentUser) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if invitation is for the current user's email
    if (invitation.email !== currentUser.email) {
      return res.status(400).json({ message: "Invitation is not for your email address" });
    }

    console.log("[DEBUG] Accepting invitation for:", {
      invitationId,
      inviterUserId: invitation.userId,
      acceptingUserId: userId,
      invitationEmail: invitation.email,
      currentUserEmail: currentUser.email
    });

    // Update invitation status
    await dbStorage.updateFamilyInvitation(invitationId, { status: 'accepted' });
    console.log("[DEBUG] Invitation status updated to accepted");

    // Create family member relationship
    const familyMemberData = {
      userId: invitation.userId,
      familyMemberId: userId,
      relationship: 'spouse'
    };
    console.log("[DEBUG] Creating family member relationship:", familyMemberData);
    
    const newFamilyMember = await dbStorage.createFamilyMember(familyMemberData);
    console.log("[DEBUG] Family member relationship created:", newFamilyMember);

    res.json({ success: true, message: "Invitation accepted successfully" });
  } catch (error) {
    console.error("Error accepting family invitation:", error);
    res.status(500).json({ message: "Failed to accept invitation" });
  }
}

/**
 * Decline family invitation
 */
async function handleDeclineFamilyInvitation(req: any, res: any) {
  try {
    const invitationId = req.params.id;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    if (!invitationId) {
      return res.status(400).json({ message: "Invitation ID is required" });
    }

    // Get the invitation
    const invitation = await dbStorage.getFamilyInvitation(invitationId);
    
    if (!invitation) {
      return res.status(404).json({ message: "Invitation not found" });
    }

    if (invitation.status !== 'pending') {
      return res.status(400).json({ message: "Invitation is no longer pending" });
    }

    // Get the current user
    const currentUser = await dbStorage.getUser(userId);
    if (!currentUser) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if invitation is for the current user's email
    if (invitation.email !== currentUser.email) {
      return res.status(400).json({ message: "Invitation is not for your email address" });
    }

    // Update invitation status
    await dbStorage.updateFamilyInvitation(invitationId, { status: 'declined' });

    res.json({ success: true, message: "Invitation declined successfully" });
  } catch (error) {
    console.error("Error declining family invitation:", error);
    res.status(500).json({ message: "Failed to decline invitation" });
  }
}

/**
 * Submit user feedback
 */
export async function submitFeedback(req: any, res: any) {
  try {
    const { message, page, url, userAgent, timestamp } = req.body;

    // Validate required fields
    if (!message || !page || !url || !userAgent || !timestamp) {
      return res.status(400).json({ 
        message: "Missing required fields: message, page, url, userAgent, timestamp" 
      });
    }

    // Validate message length
    if (message.length > 5000) {
      return res.status(400).json({ 
        message: "Feedback message is too long (maximum 5000 characters)" 
      });
    }

    // Get user information if authenticated
    let userEmail, userName;
    if (req.user) {
      userEmail = req.user.email;
      userName = req.user.firstName && req.user.lastName 
        ? `${req.user.firstName} ${req.user.lastName}` 
        : req.user.firstName || req.user.lastName || 'Unknown';
    }

    console.log(`Feedback submission from ${userEmail || 'anonymous user'} on page: ${page}`);

    // Send feedback email
    const emailSent = await sendFeedbackEmail({
      userEmail,
      userName,
      message,
      page,
      url,
      userAgent,
      timestamp
    });

    if (emailSent) {
      res.json({ 
        message: "Thank you for your feedback! We appreciate your input." 
      });
    } else {
      res.status(500).json({ 
        message: "Failed to send feedback. Please try again later." 
      });
    }

  } catch (error) {
    console.error("Feedback submission error:", error);
    res.status(500).json({ message: "Failed to process feedback" });
  }
}