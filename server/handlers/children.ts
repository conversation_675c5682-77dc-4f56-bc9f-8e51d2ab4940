import type { Express } from "express";
import { isAuthenticated } from "../googleAuth";
import { storage as dbStorage } from "../storage";
import { db } from "../db";
import { eq } from "drizzle-orm";
import * as schema from "../../shared/schema";
import crypto from "crypto";
import { upload } from "../middleware/upload";
import { FileStorageService } from "../services/fileStorage";

/**
 * Register all children-related routes
 */
export function childrenRoutes(app: Express) {
  // Get all children for authenticated user
  app.get("/api/children", handleGetChildren);

  // Create new child
  app.post("/api/children", isAuthenticated, upload.single("avatar"), handleCreateChild);

  // Upload avatar for existing child
  app.post("/api/children/:id/avatar", isAuthenticated, upload.single("avatar"), handleUploadAvatar);

  // Get single child by ID
  app.get("/api/children/:id", isAuthenticated, handleGetChild);

  // Update child (placeholder for future implementation)
  // app.put("/api/children/:id", isAuthenticated, handleUpdateChild);

  // Delete child (placeholder for future implementation)
  app.delete("/api/children/:id", isAuthenticated, handleDeleteChild);
}

/**
 * Get all children for the authenticated user (including shared via family)
 */
async function handleGetChildren(req: any, res: any) {
  try {
    // Get the authenticated user's ID
    const userId = req.user?.id;

    if (!userId) {
      console.error("[DEBUG] No user ID found in request");
      return res.status(401).json({ message: "User not authenticated" });
    }

    console.log("[DEBUG] GET /api/children - Auth user:", {
      userId,
      userObject: req.user ? JSON.stringify(req.user) : "No user",
    });

    // Use the storage method that includes family sharing
    const result = await dbStorage.getChildrenByUserId(userId);

    // Get documents for each child
    const childrenWithDocuments = await Promise.all(
      result.map(async (child) => {
        const documents = await db
          .select()
          .from(schema.documents)
          .where(eq(schema.documents.childId, child.id));

        return {
          ...child,
          documents,
          documentCount: documents.length
        };
      })
    );

    console.log("[DEBUG] Found children (including shared):", JSON.stringify(result));
    res.json(childrenWithDocuments);
  } catch (error) {
    console.error("[DEBUG] Error fetching children:", error);
    res.status(500).json({ message: "Failed to fetch children" });
  }
}

/**
 * Create a new child
 */
async function handleCreateChild(req: any, res: any) {
  try {
    const { name, birthMonth, birthYear } = req.body;
    const avatarFile = req.file;

    // Get the authenticated user's ID
    const userId = req.user?.id;

    console.log("[DEBUG] POST /api/children - Auth user:", {
      userId,
      userObject: req.user ? JSON.stringify(req.user) : "No user",
    });

    if (!name) {
      return res.status(400).json({ message: "Child name is required" });
    }

    console.log("[DEBUG] Creating child with userId:", userId, "and data:", {
      name,
      birthMonth,
      birthYear,
      hasAvatar: !!avatarFile,
    });

    // Generate a random ID for the new child
    const childId = crypto.randomUUID();

    // Handle avatar upload if provided
    let avatarUrl = null;
    if (avatarFile) {
      try {
        console.log("Uploading avatar file:", avatarFile.originalname);
        console.log("Avatar file details:", {
          mimetype: avatarFile.mimetype,
          size: avatarFile.size,
          buffer: avatarFile.buffer ? `Buffer(${avatarFile.buffer.length} bytes)` : 'No buffer'
        });
        
        // Upload avatar to file storage in avatars folder
        const uploadResult = await FileStorageService.uploadAvatar(avatarFile);
        
        // Generate a URL for accessing the avatar
        avatarUrl = `/api/files/${uploadResult.fileKey}`;
        
        console.log("Avatar uploaded successfully:", {
          fileKey: uploadResult.fileKey,
          avatarUrl: avatarUrl,
          fullPath: uploadResult.fullPath
        });
      } catch (uploadError) {
        console.error("Error uploading avatar:", uploadError);
        // Continue without avatar if upload fails
      }
    }

    // Insert directly into database
    const [newChild] = await db
      .insert(schema.children)
      .values({
        id: childId,
        name: name,
        birthMonth: birthMonth || null,
        birthYear: birthYear || null,
        avatarUrl: avatarUrl,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    console.log(
      "[DEBUG] Child created successfully:",
      JSON.stringify(newChild),
    );
    res.status(201).json(newChild);
  } catch (error) {
    console.error("[DEBUG] Error creating child:", error);
    res.status(500).json({ message: "Failed to create child" });
  }
}

/**
 * Upload avatar for existing child
 */
async function handleUploadAvatar(req: any, res: any) {
  try {
    const userId = req.user.id;
    const childId = req.params.id;
    const avatarFile = req.file;

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get child info for the response
    const child = await dbStorage.getChildById(childId);
    if (!child) {
      return res.status(404).json({ message: "Child not found" });
    }

    // Handle avatar upload if provided
    let avatarUrl = null;
    if (avatarFile) {
      try {
        console.log("Uploading avatar file:", avatarFile.originalname);
        console.log("Avatar file details:", {
          mimetype: avatarFile.mimetype,
          size: avatarFile.size,
          buffer: avatarFile.buffer ? `Buffer(${avatarFile.buffer.length} bytes)` : 'No buffer'
        });
        
        // Upload avatar to file storage in avatars folder
        const uploadResult = await FileStorageService.uploadAvatar(avatarFile);
        
        // Generate a URL for accessing the avatar
        avatarUrl = `/api/files/${uploadResult.fileKey}`;
        
        console.log("Avatar uploaded successfully:", {
          fileKey: uploadResult.fileKey,
          avatarUrl: avatarUrl,
          fullPath: uploadResult.fullPath
        });
      } catch (uploadError) {
        console.error("Error uploading avatar:", uploadError);
        // Continue without avatar if upload fails
      }
    }

    // Update child in database
    const [updatedChild] = await db
      .update(schema.children)
      .set({
        avatarUrl: avatarUrl || undefined,
        updatedAt: new Date(),
      })
      .where(eq(schema.children.id, childId))
      .returning();

    if (!updatedChild) {
      return res.status(404).json({ message: "Child not found" });
    }

    res.json(updatedChild);
  } catch (error) {
    console.error("Error uploading avatar:", error);
    res.status(500).json({ message: "Failed to upload avatar" });
  }
}

/**
 * Get single child by ID
 */
async function handleGetChild(req: any, res: any) {
  try {
    const userId = req.user.id;
    const childId = req.params.id;

    const child = await dbStorage.getChildById(childId);

    if (!child) {
      return res.status(404).json({ message: "Child not found" });
    }

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    res.json(child);
  } catch (error) {
    console.error("Error fetching child:", error);
    res.status(500).json({ message: "Failed to fetch child" });
  }
}

/**
 * Update child (placeholder for future implementation)
 */
async function handleUpdateChild(req: any, res: any) {
  try {
    const userId = req.user.id;
    const childId = req.params.id;
    const { name, birthMonth, birthYear, avatarUrl } = req.body;

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Update child in database
    const [updatedChild] = await db
      .update(schema.children)
      .set({
        name: name || undefined,
        birthMonth: birthMonth || undefined,
        birthYear: birthYear || undefined,
        avatarUrl: avatarUrl || undefined,
        updatedAt: new Date(),
      })
      .where(eq(schema.children.id, childId))
      .returning();

    if (!updatedChild) {
      return res.status(404).json({ message: "Child not found" });
    }

    res.json(updatedChild);
  } catch (error) {
    console.error("Error updating child:", error);
    res.status(500).json({ message: "Failed to update child" });
  }
}

/**
 * Delete child
 */
async function handleDeleteChild(req: any, res: any) {
  try {
    const userId = req.user.id;
    const childId = req.params.id;

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get child info for the response
    const child = await dbStorage.getChildById(childId);
    if (!child) {
      return res.status(404).json({ message: "Child not found" });
    }

    // Perform cascading deletion
    await dbStorage.deleteChildAndRelatedData(childId);

    res.json({ 
      success: true, 
      message: "Child and all related data deleted successfully",
      childName: child.name 
    });
  } catch (error) {
    console.error("Error deleting child:", error);
    res.status(500).json({ message: "Failed to delete child" });
  }
}