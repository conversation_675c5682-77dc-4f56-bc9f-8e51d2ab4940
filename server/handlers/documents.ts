import type { Express } from "express";
import { isAuthenticated } from "../googleAuth";
import { upload } from "../middleware/upload";
import { FileStorageService } from "../services/fileStorage";
import { storage as dbStorage } from "../storage";
import { db } from "../db";
import { eq } from "drizzle-orm";
import * as schema from "../../shared/schema";
import crypto from "crypto";

/**
 * Register all document-related routes
 */
export function documentRoutes(app: Express) {
  // File upload route
  app.post(
    "/api/children/:childId/documents/upload",
    isAuthenticated,
    upload.single("document"),
    handleFileUpload,
  );

  // File serving route - use wildcard to capture full path
  app.get("/api/files/*", handleFileServe);

  // Get documents for a child
  app.get(
    "/api/children/:childId/documents",
    isAuthenticated,
    handleGetDocuments,
  );

  // Get single document
  app.get("/api/documents/:id", isAuthenticated, handleGetDocument);

  // Delete document
  app.delete("/api/documents/:id", isAuthenticated, handleDeleteDocument);
}

/**
 * Handle file upload
 */
async function handleFileUpload(req: any, res: any) {
  try {
    const userId = req.user?.id || "anonymous";
    const childId = req.params.childId;
    const file = req.file;

    if (!file) {
      return res.status(400).json({
        success: false,
        message: "No file was uploaded",
      });
    }

    // Upload file to S3
    const uploadResult = await FileStorageService.uploadFile(file);

    // Store document metadata in database
    console.log(`Storing document metadata in database for child ${childId}`);

    const documentId = crypto.randomUUID();
    const [document] = await db
      .insert(schema.documents)
      .values({
        id: documentId,
        childId,
        title: file.originalname,
        documentType: "Other",
        fileUrl: uploadResult.fileUrl, // This is now the S3 key
        uploadedBy: userId,
        // fileText will be extracted lazily when first needed for AI
        fileText: null,
      })
      .returning();

    console.log(`Document saved with ID: ${document.id}`);

    // Generate presigned URL for immediate access
    const presignedUrl = await FileStorageService.getPresignedDownloadUrl(uploadResult.fileKey);

    // Send success response
    res.status(201).json({
      success: true,
      document: {
        id: document.id,
        title: document.title,
        fileUrl: presignedUrl, // Return presigned URL for client
        documentType: document.documentType,
        createdAt: document.createdAt,
      },
    });
  } catch (error: any) {
    console.error("Error processing file upload:", error);
    res.status(500).json({
      success: false,
      message: `Upload error: ${error.message}`,
    });
  }
}

/**
 * Handle file serving
 */
async function handleFileServe(req: any, res: any) {
  try {
    // With wildcard route, the full path is in req.params[0]
    const fullPath = req.params[0];
    
    console.log("File serve request:", {
      fullPath: fullPath,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer')
    });
    
    // The fullPath is already the file key (e.g., "avatars/1750567620291-8fdfa265/anna-sophia.png")
    const fileKey = fullPath;
    
    console.log("Using file key:", fileKey);
    
    const fileData = await FileStorageService.serveFile(fileKey);

    console.log("File data retrieved:", {
      contentType: fileData.contentType,
      filename: fileData.filename,
      bufferSize: fileData.buffer.length
    });

    // Set headers and send the file
    res.setHeader("Content-Type", fileData.contentType);
    res.setHeader(
      "Content-Disposition",
      `inline; filename="${fileData.filename}"`,
    );
    res.send(fileData.buffer);
  } catch (error: any) {
    console.error("Error serving file:", error);
    res.status(404).send(`File not found or error: ${error.message}`);
  }
}

/**
 * Get all documents for a child
 */
async function handleGetDocuments(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const childId = req.params.childId;

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    const documents = await dbStorage.getDocumentsByChildId(childId);
    
    // Generate presigned URLs for each document
    const documentsWithUrls = await Promise.all(
      documents.map(async (doc) => {
        try {
          const fileKey = FileStorageService.getFileKeyFromUrl(doc.fileUrl);
          const presignedUrl = await FileStorageService.getPresignedDownloadUrl(fileKey);
          return {
            ...doc,
            fileUrl: presignedUrl, // Replace S3 key with presigned URL
          };
        } catch (error) {
          console.error(`Error generating presigned URL for document ${doc.id}:`, error);
          return {
            ...doc,
            fileUrl: null, // Indicate URL generation failed
          };
        }
      })
    );
    
    res.json(documentsWithUrls);
  } catch (error) {
    console.error("Error fetching documents:", error);
    res.status(500).json({ message: "Failed to fetch documents" });
  }
}

/**
 * Get single document by ID
 */
async function handleGetDocument(req: any, res: any) {
  try {
    const userId = req.user.id;
    const documentId = req.params.id;

    const document = await dbStorage.getDocumentById(documentId);

    if (!document) {
      return res.status(404).json({ message: "Document not found" });
    }

    // Check if user has access to this document's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      document.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get child info to include in the response
    const child = await dbStorage.getChildById(document.childId);

    // Generate presigned URL for the document
    let presignedUrl = null;
    try {
      const fileKey = FileStorageService.getFileKeyFromUrl(document.fileUrl);
      presignedUrl = await FileStorageService.getPresignedDownloadUrl(fileKey);
    } catch (error) {
      console.error(`Error generating presigned URL for document ${document.id}:`, error);
    }

    // Add child name and presigned URL to the document response
    const documentWithInfo = {
      ...document,
      childName: child ? child.name : "Unknown",
      fileUrl: presignedUrl, // Replace S3 key with presigned URL
    };

    res.json(documentWithInfo);
  } catch (error) {
    console.error("Error fetching document:", error);
    res.status(500).json({ message: "Failed to fetch document" });
  }
}

/**
 * Delete document
 */
async function handleDeleteDocument(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const documentId = req.params.id;

    const document = await dbStorage.getDocumentById(documentId);

    if (!document) {
      return res.status(404).json({ message: "Document not found" });
    }

    // Check if user has access to this document's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      document.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Delete file from S3
    await FileStorageService.deleteFile(document.fileUrl);

    // Delete document record from database
    await db
      .delete(schema.documents)
      .where(eq(schema.documents.id, documentId));

    console.log(`Document deleted: ${documentId}`);
    res.json({ success: true, message: "Document deleted successfully" });
  } catch (error) {
    console.error("Error deleting document:", error);
    res.status(500).json({ message: "Failed to delete document" });
  }
}