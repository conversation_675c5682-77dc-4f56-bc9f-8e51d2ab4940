import type { Express } from "express";
import { isAuthenticated } from "../googleAuth";
import { storage as dbStorage } from "../storage";
// import { DocumentExtractionService } from "../services/documentExtraction";
import { HybridDocumentProcessor as OpenAIDocumentProcessor } from "../services/hybridDocumentProcessor";
import {
  generateDocumentSummary,
  analyzeIEPGoals,
  generateSuggestedQuestions,
  answerQuestion,
} from "../openai";
import crypto from "crypto";

/**
 * Register all AI-related routes
 */
export function aiRoutes(app: Express) {
  // Child-wide summary endpoint (uses all documents for a child)
  app.all(
    "/api/ai/summary/child/:childId",
    isAuthenticated,
    handleChildSummary,
  );

  // Document-specific summary (backward compatibility)
  app.all(
    "/api/ai/summary/:documentId",
    isAuthenticated,
    handleDocumentSummary,
  );

  // Analyze IEP goals from a document
  app.get(
    "/api/ai/goals/:documentId",
    isAuthenticated,
    handleGoalAnalysis,
  );

  // Generate suggested questions for a document
  app.get(
    "/api/ai/suggested-questions/:documentId",
    isAuthenticated,
    handleSuggestedQuestions,
  );

  // Ask a question about a child's documents
  app.post("/api/ai/ask/:childId", isAuthenticated, handleAskQuestion);
}

/**
 * Generate summary for all documents of a child
 */
async function handleChildSummary(req: any, res: any) {
  try {
    console.log("Child summary API called for child:", req.params.childId);
    console.log("Request method:", req.method);
    console.log("Request body:", req.body);
    
    const userId = req.user.id;
    const childId = req.params.childId;

    console.log("Processing child summary request:", { userId, childId });

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      console.log("Access denied for user", userId, "to child", childId);
      return res.status(403).json({ message: "Access denied" });
    }

    // Get all documents for this child
    const documents = await dbStorage.getDocumentsByChildId(childId);
    
    if (!documents || documents.length === 0) {
      return res.status(404).json({ message: "No documents found for this child" });
    }

    console.log(`Found ${documents.length} documents for child ${childId}`);

    // Check if we already have a summary for this child with the same number of documents
    const existingConversation = await dbStorage.getConversationByChildId(childId);
    
    if (existingConversation && 
        existingConversation.hasSummary && 
        existingConversation.summary &&
        existingConversation.documentCountAtSummary === documents.length) {
      console.log("Using existing summary from database");
      return res.json({ answer: existingConversation.summary });
    }

    // Add this right after you get the documents
    console.log("Environment check:");
    console.log("LAMBDA_URL:", process.env.LAMBDA_PDF_EXTRACTOR_URL);
    console.log("AWS_API_KEY set:", !!process.env.AWS_API_KEY);
    console.log("Documents found:", documents.map(d => ({ id: d.id, title: d.title, fileUrl: d.fileUrl })));

    // Extract text from all documents using Hybrid processor (Lambda + OpenAI Vision)
    const combinedDocumentText = await OpenAIDocumentProcessor.extractTextFromDocuments(documents);

    console.log("Generating summary using OpenAI...");
    
    // Generate summary using OpenAI with all documents
    const summary = await generateDocumentSummary(combinedDocumentText);

    console.log("Child summary generated successfully");
    
    // Store the summary in the database
    if (existingConversation) {
      // Update existing conversation
      await dbStorage.updateConversationSummary(existingConversation.id, summary, documents.length);
    } else {
      // Create new conversation with summary
      await dbStorage.createConversationWithSummary(childId, userId, summary, documents.length);
    }
    
    res.json({ answer: summary });
  } catch (error) {
    console.error("Error generating child summary:", error);
    res.status(500).json({ message: "Failed to generate summary" });
  }
}

/**
 * Generate summary for a specific document
 */
async function handleDocumentSummary(req: any, res: any) {
  try {
    console.log("Summary API called for document:", req.params.documentId);
    console.log("Request method:", req.method);
    console.log("Request body:", req.body);
    
    const userId = req.user.id;
    const documentId = req.params.documentId;

    console.log("Processing summary request:", { userId, documentId });

    const document = await dbStorage.getDocumentById(documentId);

    if (!document) {
      console.log("Document not found:", documentId);
      return res.status(404).json({ message: "Document not found" });
    }

    console.log("Document found:", document.id, document.title);

    // Check if user has access to this document's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      document.childId,
    );
    
    if (!hasAccess) {
      console.log("Access denied for user", userId, "to child", document.childId);
      return res.status(403).json({ message: "Access denied" });
    }

    console.log("Extracting document text using Hybrid processor...");
    const documentText = await OpenAIDocumentProcessor.extractDocumentText(document);

    console.log("=== SINGLE DOCUMENT EXTRACTION DEBUG ===");
    console.log(`Document text length: ${documentText.length} characters`);
    console.log(`First 500 characters: ${documentText.substring(0, 500)}`);
    console.log(`Last 500 characters: ${documentText.substring(Math.max(0, documentText.length - 500))}`);
    console.log("=== END SINGLE DOCUMENT EXTRACTION DEBUG ===");

    console.log("Generating summary using OpenAI...");
    
    // Generate summary using OpenAI
    const summary = await generateDocumentSummary(documentText);

    console.log("=== SINGLE DOCUMENT SUMMARY DEBUG ===");
    console.log(`Generated summary length: ${summary.length} characters`);
    console.log(`Summary preview: ${summary.substring(0, 500)}`);
    console.log("=== END SINGLE DOCUMENT SUMMARY DEBUG ===");

    console.log("Summary generated successfully");
    
    res.json({ answer: summary });
  } catch (error) {
    console.error("Error generating summary:", error);
    res.status(500).json({ message: "Failed to generate summary" });
  }
}

/**
 * Analyze IEP goals from a document
 */
async function handleGoalAnalysis(req: any, res: any) {
  try {
    const userId = req.user.id;
    const documentId = req.params.documentId;

    const document = await dbStorage.getDocumentById(documentId);

    if (!document) {
      return res.status(404).json({ message: "Document not found" });
    }

    // Check if user has access to this document's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      document.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Extract text and analyze goals using Hybrid processor (Lambda + OpenAI Vision)
    const documentText = await OpenAIDocumentProcessor.extractDocumentText(document);
    const goals = await analyzeIEPGoals(documentText);

    res.json(goals);
  } catch (error) {
    console.error("Error analyzing goals:", error);
    res.status(500).json({ message: "Failed to analyze goals" });
  }
}

/**
 * Generate suggested questions for a document
 */
async function handleSuggestedQuestions(req: any, res: any) {
  try {
    const userId = req.user.id;
    const documentId = req.params.documentId;

    const document = await dbStorage.getDocumentById(documentId);

    if (!document) {
      return res.status(404).json({ message: "Document not found" });
    }

    // Check if user has access to this document's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      document.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Extract text and generate suggested questions using Hybrid processor (Lambda + OpenAI Vision)
    const documentText = await OpenAIDocumentProcessor.extractDocumentText(document);
    const questions = await generateSuggestedQuestions(documentText);

    res.json(questions);
  } catch (error) {
    console.error("Error generating suggested questions:", error);
    res
      .status(500)
      .json({ message: "Failed to generate suggested questions" });
  }
}

/**
 * Ask a question about a child's documents
 */
async function handleAskQuestion(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const childId = req.params.childId;

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    const { question, documentId, conversationId } = req.body;

    if (!question) {
      return res.status(400).json({ message: "Question is required" });
    }

    // Get document text
    let documentText = "";
    if (documentId) {
      // Specific document
      const document = await dbStorage.getDocumentById(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      documentText = await OpenAIDocumentProcessor.extractDocumentText(document);
    } else {
      // All child's documents
      const documents = await dbStorage.getDocumentsByChildId(childId);
      documentText = await OpenAIDocumentProcessor.extractTextFromDocuments(documents);
    }

    // Answer question using OpenAI
    const answer = await answerQuestion(question, documentText);

    // Store message in conversation
    let convoId = conversationId;

    if (!convoId) {
      // Create a new conversation if no conversationId provided
      const newConversation = await dbStorage.createConversation({
        id: crypto.randomUUID(),
        childId,
        title: question.substring(0, 50) + (question.length > 50 ? "..." : ""),
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      convoId = newConversation.id;
    }

    // Store user message
    await dbStorage.createMessage({
      id: crypto.randomUUID(),
      conversationId: convoId,
      role: "user",
      content: question,
      timestamp: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Store assistant message
    await dbStorage.createMessage({
      id: crypto.randomUUID(),
      conversationId: convoId,
      role: "assistant",
      content: answer.answer,
      timestamp: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    res.json({
      ...answer,
      conversationId: convoId,
    });
  } catch (error) {
    console.error("Error answering question:", error);
    res.status(500).json({ message: "Failed to answer question" });
  }
}