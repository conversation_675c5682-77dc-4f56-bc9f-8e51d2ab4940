import type { Express } from "express";
import { isAuthenticated } from "../googleAuth";
import { storage as dbStorage } from "../storage";
import { DocumentExtractionService } from "../services/documentExtraction";
import { answerQuestion, answerQuestionStream } from "../openai";
import crypto from "crypto";

/**
 * Register all conversation-related routes
 */
export function conversationRoutes(app: Express) {
  // Get conversations for a child
  app.get(
    "/api/children/:childId/conversations",
    isAuthenticated,
    handleGetConversations,
  );

  // Create new conversation for a child
  app.post(
    "/api/children/:childId/conversations",
    isAuthenticated,
    handleCreateConversation,
  );

  // Update conversation (for hasSummary and documentCountAtSummary tracking)
  app.patch(
    "/api/conversations/:id",
    isAuthenticated,
    handleUpdateConversation,
  );

  // Get conversation by ID (with messages)
  app.get("/api/conversations/:id", isAuthenticated, handleGetConversation);

  // Get messages for a conversation
  app.get("/api/conversations/:id/messages", isAuthenticated, handleGetMessages);

  // Send message to a conversation
  app.post("/api/conversations/:id/messages", isAuthenticated, handleSendMessage);

  // Store message directly without AI response
  app.post("/api/conversations/:id/messages/direct", isAuthenticated, handleStoreMessageDirect);

  // Stream message to a conversation with SSE
  app.post("/api/conversations/:id/messages/stream", isAuthenticated, handleSendMessageStream);

  // Log message copy events for analytics
  app.post("/api/conversations/:id/messages/:messageId/copy", isAuthenticated, handleMessageCopy);
}

/**
 * Get all conversations for a child
 */
async function handleGetConversations(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const childId = req.params.childId;

    console.log("Fetching conversations for child:", childId);

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    const conversations = await dbStorage.getConversationsByChildId(childId);
    
    console.log(`Found ${conversations.length} conversations`);
    res.json(conversations);
  } catch (error) {
    console.error("Error fetching conversations:", error);
    res.status(500).json({ 
      message: "Failed to fetch conversations",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Create new conversation for a child
 */
async function handleCreateConversation(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const childId = req.params.childId;

    // Check if user has access to this child
    const hasAccess = await dbStorage.userHasAccessToChild(userId, childId);
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    const { title, hasSummary, documentCountAtSummary } = req.body;

    const conversation = await dbStorage.createConversation({
      id: crypto.randomUUID(),
      childId,
      title: title || "New Conversation",
      createdBy: userId,
      hasSummary: hasSummary || false,
      documentCountAtSummary: documentCountAtSummary || 0,
      createdAt: new Date()
    });

    res.json(conversation);
  } catch (error) {
    console.error("Error creating conversation:", error);
    res.status(500).json({ message: "Failed to create conversation" });
  }
}

/**
 * Update conversation (for tracking summary generation)
 */
async function handleUpdateConversation(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const conversationId = req.params.id;

    console.log("Updating conversation:", conversationId);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    const { title, hasSummary, documentCountAtSummary } = req.body;

    // Prepare update data (only include fields that are provided)
    const updateData: any = {};
    if (title !== undefined) updateData.title = title;
    if (hasSummary !== undefined) updateData.hasSummary = hasSummary;
    if (documentCountAtSummary !== undefined) updateData.documentCountAtSummary = documentCountAtSummary;

    const updatedConversation = await dbStorage.updateConversation(conversationId, updateData);

    console.log("Conversation updated successfully");
    res.json(updatedConversation);
  } catch (error) {
    console.error("Error updating conversation:", error);
    res.status(500).json({ 
      message: "Failed to update conversation",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Get conversation by ID with messages
 */
async function handleGetConversation(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const conversationId = req.params.id;

    console.log("Fetching conversation:", conversationId);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get messages for this conversation
    const messages = await dbStorage.getMessagesByConversationId(conversationId);

    res.json({
      ...conversation,
      messages,
    });
  } catch (error) {
    console.error("Error fetching conversation:", error);
    res.status(500).json({ 
      message: "Failed to fetch conversation",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Get messages for a conversation
 */
async function handleGetMessages(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const conversationId = req.params.id;

    console.log("Fetching messages for conversation:", conversationId);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    const messages = await dbStorage.getMessagesByConversationId(conversationId);
    
    console.log(`Returning ${messages.length} messages`);
    res.json(messages);
  } catch (error) {
    console.error("Error fetching messages:", error);
    res.status(500).json({ 
      message: "Failed to fetch messages",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Send message to a conversation
 */
async function handleSendMessage(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const conversationId = req.params.id;
    const { content, prompt } = req.body;

    if (!content) {
      return res.status(400).json({ message: "Message content is required" });
    }

    console.log("Adding message to conversation:", conversationId);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Store user message
    const userMessage = await dbStorage.createMessage({
      id: crypto.randomUUID(),
      conversationId,
      role: "user",
      content,
      timestamp: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Get all documents for this child to use as context
    const documents = await dbStorage.getDocumentsByChildId(conversation.childId);
    
    // Extract text from all documents using Lambda (now with S3 integration)
    const combinedDocumentText = await DocumentExtractionService.extractTextFromDocuments(documents);

    // Generate AI response using all documents as context
    const aiResponse = await answerQuestion(content, combinedDocumentText, prompt);

    // Store AI message
    const aiMessage = await dbStorage.createMessage({
      id: crypto.randomUUID(),
      conversationId,
      role: "assistant",
      content: aiResponse.answer,
      timestamp: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    console.log("Messages created successfully");

    res.json({
      userMessage,
      aiMessage,
      success: true,
    });
  } catch (error) {
    console.error("Error sending message:", error);
    res.status(500).json({ 
      message: "Failed to send message",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Store a message directly without generating AI response (for system messages like summaries)
 */
async function handleStoreMessageDirect(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const conversationId = req.params.id;
    const { content, role, timestamp } = req.body;

    if (!content) {
      return res.status(400).json({ message: "Message content is required" });
    }

    console.log("Storing message directly to conversation:", conversationId);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Store message directly - no AI response generation
    const message = await dbStorage.createMessage({
      id: crypto.randomUUID(),
      conversationId,
      role: role || "assistant",
      content,
      timestamp: timestamp ? new Date(timestamp) : new Date(),
    });

    console.log("Message stored directly without AI response");

    res.json({
      message,
      success: true,
    });
  } catch (error) {
    console.error("Error storing message directly:", error);
    res.status(500).json({ 
      message: "Failed to store message",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Send message to a conversation with SSE streaming
 */
async function handleSendMessageStream(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    const conversationId = req.params.id;
    const { content, prompt } = req.body;

    if (!content) {
      return res.status(400).json({ message: "Message content is required" });
    }

    console.log("Starting streaming message to conversation:", conversationId);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Store user message first
    const userMessage = await dbStorage.createMessage({
      id: crypto.randomUUID(),
      conversationId,
      role: "user",
      content,
      timestamp: new Date(),
    });

    // Send user message confirmation
    res.write(`data: ${JSON.stringify({ 
      type: 'userMessage', 
      message: userMessage 
    })}\n\n`);

    // Get all documents for this child to use as context
    const documents = await dbStorage.getDocumentsByChildId(conversation.childId);
    const combinedDocumentText = await DocumentExtractionService.extractTextFromDocuments(documents);

    // Prepare to store the AI response
    const aiMessageId = crypto.randomUUID();
    let fullAiResponse = '';

    // Send streaming response
    await answerQuestionStream(
      content,
      combinedDocumentText,
      // onChunk callback - send each token
      (chunk: string) => {
        fullAiResponse += chunk;
        res.write(`data: ${JSON.stringify({ 
          type: 'chunk', 
          content: chunk,
          messageId: aiMessageId 
        })}\n\n`);
      },
      // onComplete callback - save final message and close stream
      async () => {
        // Store complete AI message in database
        const aiMessage = await dbStorage.createMessage({
          id: aiMessageId,
          conversationId,
          role: "assistant",
          content: fullAiResponse,
          timestamp: new Date(),
        });

        // Send completion signal
        res.write(`data: ${JSON.stringify({ 
          type: 'complete', 
          message: aiMessage 
        })}\n\n`);

        res.end();
        console.log("Streaming message completed successfully");
      },
      // onError callback
      (error: Error) => {
        console.error("Error in streaming message:", error);
        res.write(`data: ${JSON.stringify({ 
          type: 'error', 
          error: error.message 
        })}\n\n`);
        res.end();
      }
    );

  } catch (error) {
    console.error("Error in handleSendMessageStream:", error);
    res.write(`data: ${JSON.stringify({ 
      type: 'error', 
      error: 'Failed to send streaming message' 
    })}\n\n`);
    res.end();
  }
}

/**
 * Log message copy events for analytics
 */
async function handleMessageCopy(req: any, res: any) {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }
    
    const { id: conversationId, messageId } = req.params;

    console.log(`User ${userId} copied message ${messageId} from conversation ${conversationId}`);

    const conversation = await dbStorage.getConversationById(conversationId);

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    // Check if user has access to this conversation's child
    const hasAccess = await dbStorage.userHasAccessToChild(
      userId,
      conversation.childId,
    );
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Store this in analytics tracking
    await dbStorage.logUserAction(userId, 'message_copy', { conversationId, messageId });
    
    res.json({ success: true });
  } catch (error) {
    console.error("Error logging message copy:", error);
    res.status(500).json({ 
      message: "Failed to log copy event",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}