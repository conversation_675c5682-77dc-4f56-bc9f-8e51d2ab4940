// Delete a document
router.delete("/documents/:id", isAuthenticated, async (req: any, res) => {
  try {
    const userId = req.user?.claims?.sub || req.user?.id;
    const documentId = req.params.id;

    console.log(
      `[DEBUG] Attempting to delete document ${documentId} for user ${userId}`,
    );

    // Get document to check ownership and get storage key
    const document = await dbStorage.getDocumentById(documentId);

    if (!document) {
      console.log(`[DEBUG] Document ${documentId} not found in database`);
      return res.status(404).json({ message: "Document not found" });
    }

    console.log(`[DEBUG] Found document:`, {
      id: document.id,
      title: document.title,
      fileUrl: document.fileUrl,
      childId: document.childId,
    });

    // Track deletion success/failure for response
    let storageDeleted = false;
    let databaseDeleted = false;
    let storageError = null;

    // Attempt to delete from storage first if file exists
    if (document.fileUrl) {
      try {
        // Extract storage key from fileUrl
        // URL format: /api/documents/documents/timestamp-randomId.ext
        // We need: documents/timestamp-randomId.ext
        let storageKey;

        if (document.fileUrl.startsWith("/api/documents/")) {
          // Remove the /api/documents/ prefix to get the full storage key
          storageKey = document.fileUrl.replace("/api/documents/", "");
        } else {
          // Fallback: assume the last part is the filename and prepend documents/
          const filename = document.fileUrl.split("/").pop();
          storageKey = `documents/${filename}`;
        }

        console.log(
          `[DEBUG] Extracted storage key: ${storageKey} from URL: ${document.fileUrl}`,
        );

        if (storageKey) {
          const deleteResult = await FileStorage.deleteFile(storageKey);
          if (deleteResult) {
            console.log(
              `[DEBUG] Successfully deleted file from storage: ${storageKey}`,
            );
            storageDeleted = true;
          } else {
            console.log(
              `[DEBUG] Storage deletion returned false for key: ${storageKey}`,
            );
            storageError = "Storage deletion returned false";
          }
        } else {
          console.log(
            `[DEBUG] Could not extract storage key from URL: ${document.fileUrl}`,
          );
          storageError = "Could not extract storage key from URL";
        }
      } catch (error: any) {
        console.error(`[DEBUG] Error deleting file from storage:`, error);
        storageError = error.message || "Unknown storage error";
      }
    } else {
      console.log(`[DEBUG] No fileUrl found, skipping storage deletion`);
      storageDeleted = true; // Consider it successful if there's no file to delete
    }

    // Always attempt database deletion, regardless of storage deletion result
    try {
      const dbResult = await dbStorage.deleteDocument(documentId);
      console.log(`[DEBUG] Database deletion result:`, dbResult);

      if (dbResult) {
        databaseDeleted = true;
        console.log(
          `[DEBUG] Successfully deleted document ${documentId} from database`,
        );
      } else {
        console.log(
          `[DEBUG] Database deletion returned false for document ${documentId}`,
        );
        throw new Error("Database deletion returned false");
      }
    } catch (dbError: any) {
      console.error(`[DEBUG] Error deleting document from database:`, dbError);

      // If database deletion fails, this is a critical error
      return res.status(500).json({
        message: "Failed to delete document from database",
        error: dbError.message,
        details: {
          storageDeleted,
          storageError,
          databaseDeleted: false,
        },
      });
    }

    // Determine response based on what succeeded
    if (databaseDeleted && storageDeleted) {
      // Complete success
      console.log(
        `[DEBUG] Document ${documentId} deleted successfully from both storage and database`,
      );
      res.json({
        message: "Document deleted successfully",
        details: {
          storageDeleted: true,
          databaseDeleted: true,
        },
      });
    } else if (databaseDeleted && !storageDeleted) {
      // Database deleted but storage failed - still consider this a success with warning
      console.log(
        `[DEBUG] Document ${documentId} deleted from database but storage cleanup failed: ${storageError}`,
      );
      res.json({
        message:
          "Document deleted successfully (file cleanup failed but can be ignored)",
        warning: `Storage cleanup failed: ${storageError}`,
        details: {
          storageDeleted: false,
          storageError,
          databaseDeleted: true,
        },
      });
    } else {
      // This shouldn't happen given our logic above, but just in case
      console.error(
        `[DEBUG] Unexpected deletion state for document ${documentId}`,
      );
      res.status(500).json({
        message: "Document deletion completed with unexpected state",
        details: {
          storageDeleted,
          storageError,
          databaseDeleted,
        },
      });
    }
  } catch (error: any) {
    console.error(`[DEBUG] Unexpected error in document deletion:`, error);
    console.error(`[DEBUG] Error stack:`, error.stack);

    res.status(500).json({
      message: "Failed to delete document due to unexpected error",
      error: error.message,
      stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }
});
