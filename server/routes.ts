import type { Express } from "express";
import { createServer, type Server } from "http";
import { setupAuth } from "./googleAuth";

// Import all handler modules
import { authRoutes } from "./handlers/auth";
import { childrenRoutes } from "./handlers/children";
import { documentRoutes } from "./handlers/documents";
import { conversationRoutes } from "./handlers/conversations";
import { aiRoutes } from "./handlers/ai";

/**
 * Register all routes with the Express app
 */
export async function registerRoutes(app: Express): Promise<Server> {
  console.log("Setting up authentication...");
  // Set up authentication first
  await setupAuth(app);

  console.log("Registering route handlers...");
  
  // Register all route handlers
  authRoutes(app);
  childrenRoutes(app);
  documentRoutes(app);
  conversationRoutes(app);
  aiRoutes(app);

  console.log("All routes registered successfully");

  // Create and return HTTP server
  const httpServer = createServer(app);
  return httpServer;
}