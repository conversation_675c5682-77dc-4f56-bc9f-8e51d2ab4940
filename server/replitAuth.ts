import * as client from "openid-client";
import { Strategy, type VerifyFunction } from "openid-client/passport";

import passport from "passport";
import session from "express-session";
import type { Express, RequestHandler } from "express";
import memoize from "memoizee";
import connectPg from "connect-pg-simple";
import { storage } from "./storage";

if (!process.env.REPLIT_DOMAINS) {
  throw new Error("Environment variable REPLIT_DOMAINS not provided");
}

const getOidcConfig = memoize(
  async () => {
    return await client.discovery(
      new URL(process.env.ISSUER_URL ?? "https://replit.com/oidc"),
      process.env.REPL_ID!
    );
  },
  { maxAge: 3600 * 1000 }
);

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000; // 1 week
  const pgStore = connectPg(session);
  const sessionStore = new pgStore({
    conString: process.env.DATABASE_URL,
    createTableIfMissing: false,
    ttl: sessionTtl,
    tableName: "sessions",
  });
  
  const isProduction = process.env.NODE_ENV === 'production';
  
  return session({
    secret: process.env.SESSION_SECRET || "dev-secret-key",
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      secure: isProduction, // Only use secure in production
      sameSite: isProduction ? 'none' : 'lax', // Use 'none' for cross-site in production
      maxAge: sessionTtl,
    },
  });
}

function updateUserSession(
  user: any,
  tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers
) {
  user.claims = tokens.claims();
  user.access_token = tokens.access_token;
  user.refresh_token = tokens.refresh_token;
  user.expires_at = user.claims?.exp;
}

async function upsertUser(
  claims: any,
) {
  await storage.upsertUser({
    id: claims["sub"],
    email: claims["email"],
    firstName: claims["first_name"],
    lastName: claims["last_name"],
    profileImageUrl: claims["profile_image_url"],
  });
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  
  // Apply session middleware
  const sessionMiddleware = getSession();
  app.use(sessionMiddleware);
  
  // Initialize passport
  app.use(passport.initialize());
  app.use(passport.session());
  
  // Add CORS headers for auth routes specifically
  app.use('/api/auth', (req, res, next) => {
    res.header('Access-Control-Allow-Credentials', 'true');
    next();
  });

  // Add a route to check authentication status
  app.get('/api/auth/status', (req, res) => {
    if (req.isAuthenticated()) {
      res.json({
        authenticated: true,
        user: req.user
      });
    } else {
      res.json({
        authenticated: false,
        message: "User not authenticated"
      });
    }
  });

  const config = await getOidcConfig();

  const verify: VerifyFunction = async (
    tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers,
    verified: passport.AuthenticateCallback
  ) => {
    try {
      // Get claims safely with type checking
      const claims = tokens.claims();
      const userClaims = {
        sub: claims?.sub || "",
        email: claims?.email || "",
        first_name: claims?.first_name || "",
        last_name: claims?.last_name || "",
        profile_image_url: claims?.profile_image_url || ""
      };
      
      console.log("Authentication successful, claims received for user:", userClaims.sub);
      
      // Create a user object with necessary information
      const user = {
        id: userClaims.sub,
        email: userClaims.email,
        firstName: userClaims.first_name,
        lastName: userClaims.last_name,
        profileImageUrl: userClaims.profile_image_url
      };
      
      // Update session with tokens
      updateUserSession(user, tokens);
      
      // Save user to database (if we have claims)
      if (claims) {
        await upsertUser(claims);
      }
      
      // Return the user object to be serialized
      verified(null, user);
    } catch (error) {
      console.error("Error in verify function:", error);
      verified(error as Error);
    }
  };

  for (const domain of process.env
    .REPLIT_DOMAINS!.split(",")) {
    const strategy = new Strategy(
      {
        name: `replitauth:${domain}`,
        config,
        scope: "openid email profile offline_access",
        callbackURL: `https://${domain}/api/callback`,
      },
      verify,
    );
    passport.use(strategy);
  }

  passport.serializeUser((user: Express.User, cb) => cb(null, user));
  passport.deserializeUser((user: Express.User, cb) => cb(null, user));

  app.get("/api/login", (req, res, next) => {
    passport.authenticate(`replitauth:${req.hostname}`, {
      prompt: "login consent",
      scope: ["openid", "email", "profile", "offline_access"],
    })(req, res, next);
  });

  app.get("/api/callback", (req, res, next) => {
    passport.authenticate(`replitauth:${req.hostname}`, (err: Error | null, user: any) => {
      if (err) { 
        console.error("Authentication error:", err);
        return res.redirect('/api/login'); 
      }
      
      if (!user) { 
        console.error("No user returned from authentication");
        return res.redirect('/api/login'); 
      }
      
      // Log the user in manually
      req.login(user, (loginErr: Error | null) => {
        if (loginErr) {
          console.error("Login error:", loginErr);
          return res.redirect('/api/login');
        }
        
        console.log("User successfully logged in:", user.id);
        
        // Send to dashboard
        return res.redirect('/dashboard');
      });
    })(req, res, next);
  });

  app.get("/api/logout", (req, res) => {
    req.logout(() => {
      res.redirect(
        client.buildEndSessionUrl(config, {
          client_id: process.env.REPL_ID!,
          post_logout_redirect_uri: `${req.protocol}://${req.hostname}`,
        }).href
      );
    });
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  // TEMPORARY DEV MODE: Always authenticate for testing the document upload
  if (process.env.NODE_ENV === 'development') {
    // Create a mock user for development if not authenticated
    if (!req.isAuthenticated()) {
      req.user = {
        id: 'test-user-123',
        claims: {
          sub: 'test-user-123',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User'
        },
        expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        access_token: 'mock-token'
      };
      console.log("Development mode: Using test user for authentication");
    }
    return next();
  }
  
  // NORMAL AUTH LOGIC FOR PRODUCTION
  const user = req.user as any;

  if (!req.isAuthenticated()) {
    console.log("Authentication check failed: User not authenticated");
    return res.status(401).json({ message: "Unauthorized" });
  }

  const now = Math.floor(Date.now() / 1000);
  if (!user.expires_at || now > user.expires_at) {
    console.log("Token expired, attempting refresh");
    
    const refreshToken = user.refresh_token;
    if (!refreshToken) {
      console.log("No refresh token available");
      return res.status(401).json({ message: "Session expired" });
    }

    try {
      const config = await getOidcConfig();
      const tokenResponse = await client.refreshTokenGrant(config, refreshToken);
      updateUserSession(user, tokenResponse);
      console.log("Token refreshed successfully");
      return next();
    } catch (error) {
      console.error("Token refresh failed:", error);
      return res.status(401).json({ message: "Session expired" });
    }
  }
  
  return next();
};
