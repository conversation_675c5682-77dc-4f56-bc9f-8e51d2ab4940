import { db } from "../db";
import { eq } from "drizzle-orm";
import * as schema from "../../shared/schema";
import { FileStorageService } from "./fileStorage";
import OpenAI from 'openai';
import * as path from 'path';
// Note: unpdf and mammoth are imported dynamically in their respective methods

// Use the same interface as the original
export interface ExtractionResult {
  extractedText: string;
  extractionMethod: 'openai-vision' | 'unpdf' | 'text-file' | 'word-doc' | 'cached' | 'error';
  characterCount: number;
  wordCount?: number;
  processingTimeMs?: number;
  extractedTextS3Location?: {
    bucket: string;
    key: string;
  };
  timestamp: string;
}

export class OpenAIDocumentProcessor {
  private static openai: OpenAI | null = null;

  /**
   * Initialize OpenAI client
   */
  private static getOpenAIClient(): OpenAI {
    if (!this.openai) {
      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error('OPENAI_API_KEY environment variable not set');
      }
      this.openai = new OpenAI({ apiKey });
    }
    return this.openai;
  }

  /**
   * Extract text from a document using improved methods (maintains same interface as DocumentExtractionService)
   */
  static async extractDocumentText(document: any): Promise<string> {
    try {
      console.log(`Extracting text from document: ${document.title}`);
      
      // If text already extracted, return it (cached)
      if (document.fileText) {
        console.log(`Using cached text for document: ${document.id}`);
        return document.fileText;
      }

      console.log(`DEBUG: About to extract from document:`, {
        id: document.id,
        title: document.title,
        fileUrl: document.fileUrl,
        hasFileText: !!document.fileText
      });

      // Extract text using new hybrid approach
      console.log(`Extracting text for document: ${document.id} using hybrid approach`);
      const result = await this.extractWithHybridApproach(document);
      
      // Log extraction details
      console.log(`Extraction complete for ${document.id}:`);
      console.log(`- Method: ${result.extractionMethod}`);
      console.log(`- Characters: ${result.characterCount}`);
      console.log(`- Words: ${result.wordCount || 'N/A'}`);
      console.log(`- Processing time: ${result.processingTimeMs || 'N/A'}ms`);
      
      // Store extracted text in database for future use
      console.log(`Storing extracted text in database (${result.extractedText.length} characters)`);
      await db
        .update(schema.documents)
        .set({ fileText: result.extractedText })
        .where(eq(schema.documents.id, document.id));

      console.log(`Text extraction completed successfully for document: ${document.id}`);
      return result.extractedText;
      
    } catch (error) {
      console.error(`Error extracting text from document ${document.id}:`, error);
      const errorMessage = `Error processing "${document.title}": ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try re-uploading the document.`;
      
      // Store the error message so we don't keep trying to extract
      await db
        .update(schema.documents)
        .set({ fileText: errorMessage })
        .where(eq(schema.documents.id, document.id));
      
      return errorMessage;
    }
  }

  /**
   * Hybrid extraction approach: detect file type and use appropriate method
   */
  private static async extractWithHybridApproach(document: any): Promise<ExtractionResult> {
    const startTime = Date.now();
    
    try {
      // Determine file type from document title/fileUrl
      const fileExtension = this.getFileExtension(document.title || document.fileUrl);
      console.log(`Detected file type: ${fileExtension} for document: ${document.title}`);
      
      let result: Partial<ExtractionResult>;
      let extractionMethod: string;
      
      switch (fileExtension.toLowerCase()) {
        case '.txt':
          console.log('Processing as text file...');
          result = await this.extractFromTextFile(document);
          extractionMethod = 'text-file';
          break;
          
        case '.doc':
        case '.docx':
          console.log('Processing as Word document...');
          result = await this.extractFromWordDocument(document);
          extractionMethod = 'word-doc';
          break;
          
        case '.pdf':
          console.log('Processing as PDF document...');
          // Try PDF text extraction first
          result = await this.extractFromPDF(document);
          extractionMethod = 'unpdf';
          
          // If PDF extraction fails, fall back to OpenAI Vision
          if (!result.extractedText || result.extractedText.length === 0) {
            console.log('PDF text extraction insufficient, trying OpenAI Vision API...');
            result = await this.extractWithOpenAIVision(document);
            extractionMethod = 'openai-vision';
          }
          break;
          
        default:
          throw new Error(`Unsupported file type: ${fileExtension}. Supported formats: .txt, .doc, .docx, .pdf`);
      }
      
      if (!result.extractedText || result.extractedText.length === 0) {
        throw new Error(`No text could be extracted from ${fileExtension} file`);
      }
      
      const processingTime = Date.now() - startTime;
      return {
        extractedText: result.extractedText,
        extractionMethod: extractionMethod as any,
        characterCount: result.characterCount || result.extractedText.length,
        wordCount: result.wordCount,
        processingTimeMs: processingTime,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Hybrid extraction failed:', error);
      throw new Error(`Text extraction failed: ${error instanceof Error ? error.message : 'Unknown extraction error'}`);
    }
  }

  /**
   * Get file extension from filename
   */
  private static getFileExtension(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    return ext || '';
  }

  /**
   * Extract text from plain text files
   */
  private static async extractFromTextFile(document: any): Promise<Partial<ExtractionResult>> {
    try {
      console.log(`Processing text file: ${document.title}`);
      
      // Download the file
      const fileKey = FileStorageService.getFileKeyFromUrl(document.fileUrl);
      const fileBuffer = await FileStorageService.downloadFile(fileKey);
      
      console.log(`Downloaded text file (${fileBuffer.length} bytes)`);
      
      // Convert buffer to text (assuming UTF-8 encoding)
      const extractedText = fileBuffer.toString('utf-8');
      const cleanedText = this.cleanExtractedText(extractedText);
      
      console.log(`Text file extraction successful: ${cleanedText.length} characters`);
      
      return {
        extractedText: cleanedText,
        characterCount: cleanedText.length,
        wordCount: cleanedText.split(/\s+/).filter(word => word.length > 0).length
      };
      
    } catch (error) {
      console.error('Text file extraction failed:', error);
      throw new Error(`Failed to extract text from file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract text from Word documents (.doc, .docx)
   */
  private static async extractFromWordDocument(document: any): Promise<Partial<ExtractionResult>> {
    try {
      console.log(`Processing Word document: ${document.title}`);
      
      // Download the file
      const fileKey = FileStorageService.getFileKeyFromUrl(document.fileUrl);
      const fileBuffer = await FileStorageService.downloadFile(fileKey);
      
      console.log(`Downloaded Word document (${fileBuffer.length} bytes)`);
      
      // Import mammoth dynamically
      const mammoth = await import('mammoth');
      
      // Extract text from Word document
      const result = await mammoth.extractRawText({ buffer: fileBuffer });
      const extractedText = result.value;
      const cleanedText = this.cleanExtractedText(extractedText);
      
      console.log(`Word document extraction successful: ${cleanedText.length} characters`);
      
      if (result.messages && result.messages.length > 0) {
        console.log('Mammoth extraction messages:', result.messages);
      }
      
      return {
        extractedText: cleanedText,
        characterCount: cleanedText.length,
        wordCount: cleanedText.split(/\s+/).filter(word => word.length > 0).length
      };
      
    } catch (error) {
      console.error('Word document extraction failed:', error);
      throw new Error(`Failed to extract text from Word document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract text directly from PDF using unpdf (for text-based PDFs)
   */
  private static async extractFromPDF(document: any): Promise<Partial<ExtractionResult>> {
    try {
      console.log(`Attempting PDF text extraction for: ${document.title}`);
      
      // Download the PDF file
      const fileKey = FileStorageService.getFileKeyFromUrl(document.fileUrl);
      const fileBuffer = await FileStorageService.downloadFile(fileKey);
      
      console.log(`Downloaded PDF for parsing (${fileBuffer.length} bytes)`);
      
      // Import unpdf dynamically
      const { extractText } = await import('unpdf');
      
      // Convert Buffer to Uint8Array (unpdf requirement)
      const uint8Array = new Uint8Array(fileBuffer);
      
      // Parse the PDF using unpdf
      const result = await extractText(uint8Array);
      const { text, totalPages } = result;

      // ADD THIS SAFETY CHECK IMMEDIATELY AFTER
      console.log(`=== UNPDF EXTRACTION RESULT ===`);
      //console.log(`Full result:`, result);
      console.log(`typeof text:`, typeof text);
      console.log(`text is null:`, text === null);
      console.log(`text is undefined:`, text === undefined);
      console.log(`text length:`, text?.length);
      console.log(`totalPages:`, totalPages);

      // CRITICAL: Check if text is valid before proceeding
      if (text === null || text === undefined || typeof text !== 'string') {
        console.log(`ERROR: UNPDF returned invalid text - type: ${typeof text}, value:`, text);
        console.log(`Returning empty result to trigger fallback`);
        return {
          extractedText: '',
          characterCount: 0
        };
      }

      // Only proceed with valid text
      console.log(`SUCCESS: UNPDF returned valid text string`);

      // CLEAN THE TEXT FIRST
      const cleanedText = this.cleanExtractedText(text);
      console.log(`Cleaned text length: ${cleanedText.length} characters`);

      // Smart validation based on page count and character density
      const pageCount = totalPages || 1;
      const charactersPerPage = cleanedText.length / pageCount;
      
      // Different thresholds based on document size
      let minimumExpectedCharsPerPage;
      let minimumTotalChars;
      
      if (pageCount >= 10) {
        // Large documents (like IEPs) should have substantial text
        minimumExpectedCharsPerPage = 200; // Higher expectation for multi-page docs
        minimumTotalChars = pageCount * minimumExpectedCharsPerPage;
      } else if (pageCount >= 3) {
        // Medium documents
        minimumExpectedCharsPerPage = 100;
        minimumTotalChars = pageCount * minimumExpectedCharsPerPage;
      } else {
        // Small documents - more lenient
        minimumExpectedCharsPerPage = 50;
        minimumTotalChars = Math.max(500, pageCount * minimumExpectedCharsPerPage);
      }
      
      console.log(`=== PDF VALIDATION METRICS ===`);
      console.log(`=== PDF VALIDATION METRICS ===`);
      console.log(`=== PDF VALIDATION METRICS ===`);
      console.log(`Pages: ${pageCount}`);
      console.log(`Actual characters extracted: ${cleanedText.length}`);
      console.log(`Characters per page (actual): ${charactersPerPage.toFixed(2)}`);
      console.log(`Expected chars/page threshold: ${minimumExpectedCharsPerPage}`);
      console.log(`Minimum total chars expected: ${minimumTotalChars}`);
      console.log(`Meets char/page threshold: ${charactersPerPage >= minimumExpectedCharsPerPage}`);
      console.log(`Meets total chars threshold: ${cleanedText.length >= minimumTotalChars}`);
      console.log(`Overall validation result: ${(cleanedText.length >= minimumTotalChars && charactersPerPage >= minimumExpectedCharsPerPage) ? 'PASS' : 'FAIL'}`);
      console.log(`=== END PDF VALIDATION METRICS ===`);
      console.log(`=== END PDF VALIDATION METRICS ===`);
      console.log(`=== END PDF VALIDATION METRICS ===`);
      
      // Show word count and other metrics
      const wordCount = cleanedText.split(/\s+/).filter(word => word.length > 0).length;
      const wordsPerPage = wordCount / pageCount;
      console.log(`Word count: ${wordCount} (${wordsPerPage.toFixed(2)} words/page)`);
      
      // Show some text quality indicators
      const hasFormattingChars = /[A-Za-z0-9]/.test(cleanedText);
      const hasRepeatedChars = /(.)\1{10,}/.test(cleanedText); // 10+ repeated chars might indicate OCR issues
      const hasGibberish = /[^\w\s\.\,\;\:\!\?\-\(\)\[\]\"\'\/\\]{5,}/.test(cleanedText); // Many special chars in sequence
      
      console.log(`=== TEXT QUALITY INDICATORS ===`);
      console.log(`Contains alphanumeric characters: ${hasFormattingChars}`);
      console.log(`Has repeated character sequences (10+): ${hasRepeatedChars}`);
      console.log(`Has potential gibberish/OCR errors: ${hasGibberish}`);
      
      if (cleanedText.length < minimumTotalChars || charactersPerPage < minimumExpectedCharsPerPage) {
        console.log(`PDF text extraction deemed insufficient - will trigger Vision API fallback`);
        console.log(`Reason: ${cleanedText.length < minimumTotalChars ? 'Total chars too low' : ''} ${charactersPerPage < minimumExpectedCharsPerPage ? 'Chars per page too low' : ''}`);
        return {
          extractedText: '',
          characterCount: 0
        };
      }
      
      console.log(`PDF extraction successful and meets thresholds!`);
      console.log(`=== END PDF EXTRACTION DEBUG ===`);
      
      return {
        extractedText: cleanedText,
        characterCount: cleanedText.length,
        wordCount: wordCount
      };
      
    } catch (error) {
      console.error('PDF extraction failed with error:', error);
      console.log('Will fall back to OpenAI Vision API');
      return {
        extractedText: '',
        characterCount: 0
      };
    }
  }

  /**
   * Extract text using OpenAI Vision API
   */
  private static async extractWithOpenAIVision(document: any): Promise<Partial<ExtractionResult>> {
    try {
      const openai = this.getOpenAIClient();
      
      // Get the file from S3 (we'll need to download it temporarily)
      const fileBuffer = await this.downloadFileFromS3(document.fileUrl);
      
      // Convert PDF to images or handle direct image upload
      // For now, let's assume we can send the PDF directly
      const base64File = fileBuffer.toString('base64');
      
      console.log(`Sending document to OpenAI Vision API...`);
      
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Please extract all text from this document. This appears to be an educational document (likely an IEP - Individualized Education Plan). 

Extract the text exactly as it appears, maintaining the structure and formatting as much as possible. Include:
- All form fields and their values
- Table contents
- Goals and objectives
- Present levels of performance
- Accommodations and modifications
- Service delivery information

Return the text in a clear, organized format that preserves the document's structure.`
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:application/pdf;base64,${base64File}`,
                  detail: "high"
                }
              }
            ]
          }
        ],
        max_tokens: 4000
      });

      const extractedText = response.choices[0]?.message?.content || '';
      const cleanedText = this.cleanExtractedText(extractedText);
      
      console.log(`OpenAI Vision extraction completed (${cleanedText.length} characters)`);
      
      return {
        extractedText: cleanedText,
        characterCount: cleanedText.length,
        wordCount: cleanedText.split(/\s+/).length
      };
      
    } catch (error) {
      console.error('OpenAI Vision extraction failed:', error);
      throw new Error(`OpenAI Vision extraction failed: ${error instanceof Error ? error.message : 'Unknown OpenAI error'}`);
    }
  }

  /**
   * Download file from S3 to memory using existing FileStorageService
   */
  private static async downloadFileFromS3(fileUrl: string): Promise<Buffer> {
    try {
      console.log(`Downloading file using FileStorageService: ${fileUrl}`);
      
      // Use the existing FileStorageService which handles authentication
      const fileKey = FileStorageService.getFileKeyFromUrl(fileUrl);
      console.log(`Converted to file key: ${fileKey}`);
      
      const buffer = await FileStorageService.downloadFile(fileKey);
      console.log(`Successfully downloaded file (${buffer.length} bytes)`);
      
      return buffer;
      
    } catch (error) {
      console.error('Failed to download file from S3:', error);
      throw error;
    }
  }

  /**
   * Clean and normalize extracted text (same as original)
   */
  private static cleanExtractedText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'No text could be extracted from this document.';
    }

    return text
      .replace(/\r\n/g, '\n')          // Normalize line breaks
      .replace(/\r/g, '\n')            // Handle old Mac line breaks  
      .replace(/\n{3,}/g, '\n\n')      // Replace multiple newlines
      .replace(/[ \t]+/g, ' ')         // Replace multiple spaces
      .replace(/^\s+|\s+$/gm, '')      // Trim each line
      .trim();                         // Trim overall
  }

  /**
   * Extract text from multiple documents and combine (same interface as original)
   */
  static async extractTextFromDocuments(documents: any[]): Promise<string> {
    let combinedText = "";
    
    for (const doc of documents) {
      const extractedText = await this.extractDocumentText(doc);
      combinedText += `\n\n--- ${doc.title} ---\n${extractedText}`;
    }
    
    return combinedText;
  }

  /**
   * Health check for OpenAI service
   */
  static async healthCheck(): Promise<boolean> {
    try {
      const openai = this.getOpenAIClient();
      // Simple test call to verify API key works
      await openai.models.list();
      return true;
    } catch (error) {
      console.error('OpenAI health check failed:', error);
      return false;
    }
  }
}