/**
 * Google reCAPTCHA v3 verification service
 */

interface RecaptchaResponse {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

interface VerificationResult {
  success: boolean;
  score?: number;
  error?: string;
}

/**
 * Verify reCAPTCHA token with Google's API
 */
export async function verifyRecaptchaToken(
  token: string,
  expectedAction?: string,
  minimumScore: number = 0.5
): Promise<VerificationResult> {
  try {
    const secretKey = process.env.RECAPTCHA_SECRET_KEY;
    
    if (!secretKey) {
      console.error('RECAPTCHA_SECRET_KEY environment variable not set');
      return {
        success: false,
        error: 'reCAPTCHA configuration error'
      };
    }

    if (!token) {
      return {
        success: false,
        error: 'reCAPTCHA token is required'
      };
    }

    // Make request to Google's reCAPTCHA verification API
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: secretKey,
        response: token,
      }),
    });

    if (!response.ok) {
      console.error('reCAPTCHA API request failed:', response.status, response.statusText);
      return {
        success: false,
        error: 'Failed to verify reCAPTCHA'
      };
    }

    const data: RecaptchaResponse = await response.json();

    // Log the response for debugging (remove in production)
    console.log('reCAPTCHA verification response:', {
      success: data.success,
      score: data.score,
      action: data.action,
      hostname: data.hostname,
      errors: data['error-codes']
    });

    // Check if verification was successful
    if (!data.success) {
      const errorCodes = data['error-codes'] || [];
      console.error('reCAPTCHA verification failed:', errorCodes);
      
      // Map common error codes to user-friendly messages
      let errorMessage = 'reCAPTCHA verification failed';
      if (errorCodes.includes('timeout-or-duplicate')) {
        errorMessage = 'reCAPTCHA token expired. Please try again.';
      } else if (errorCodes.includes('invalid-input-response')) {
        errorMessage = 'Invalid reCAPTCHA token. Please try again.';
      } else if (errorCodes.includes('missing-input-response')) {
        errorMessage = 'reCAPTCHA verification required. Please try again.';
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }

    // Check score (reCAPTCHA v3 returns a score from 0.0 to 1.0)
    const score = data.score || 0;
    if (score < minimumScore) {
      console.warn(`reCAPTCHA score too low: ${score} (minimum: ${minimumScore})`);
      return {
        success: false,
        score,
        error: 'Security verification failed. Please try again.'
      };
    }

    // Optionally verify the action matches what we expect
    if (expectedAction && data.action !== expectedAction) {
      console.warn(`reCAPTCHA action mismatch: expected "${expectedAction}", got "${data.action}"`);
      return {
        success: false,
        error: 'Security verification failed. Please try again.'
      };
    }

    console.log(`reCAPTCHA verification successful. Score: ${score}`);
    return {
      success: true,
      score
    };

  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error);
    return {
      success: false,
      error: 'Failed to verify reCAPTCHA. Please try again.'
    };
  }
}

/**
 * Middleware-style function for Express routes
 */
export function createRecaptchaMiddleware(
  expectedAction?: string,
  minimumScore: number = 0.5
) {
  return async (req: any, res: any, next: any) => {
    const recaptchaToken = req.body.recaptchaToken;
    
    // Skip reCAPTCHA in development if token is not provided
    if (process.env.NODE_ENV === 'development' && !recaptchaToken) {
      console.log('Skipping reCAPTCHA verification in development mode');
      return next();
    }
    
    const result = await verifyRecaptchaToken(recaptchaToken, expectedAction, minimumScore);
    
    if (!result.success) {
      return res.status(400).json({
        message: result.error || 'reCAPTCHA verification failed',
        code: 'RECAPTCHA_VERIFICATION_FAILED'
      });
    }
    
    // Add score to request for potential use in handlers
    req.recaptchaScore = result.score;
    next();
  };
}