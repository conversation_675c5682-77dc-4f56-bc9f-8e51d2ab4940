import { db } from "../db";
import { eq } from "drizzle-orm";
import * as schema from "../../shared/schema";
import { FileStorageService } from "./fileStorage";
import OpenAI from 'openai';
import * as path from 'path';

// Use the same interface as the original OpenAI processor
export interface ExtractionResult {
  extractedText: string;
  extractionMethod: 'lambda-unpdf' | 'lambda-ocr' | 'openai-vision' | 'text-file' | 'word-doc' | 'cached' | 'error';
  characterCount: number;
  wordCount?: number;
  processingTimeMs?: number;
  extractedTextS3Location?: {
    bucket: string;
    key: string;
  };
  timestamp: string;
  qualityMetrics?: any;
}

interface LambdaResponse {
  success: boolean;
  needsVisionProcessing?: boolean;
  reason?: string;
  message?: string;
  extractedText?: string;
  extractionMethod?: string;
  characterCount?: number;
  wordCount?: number;
  processingTimeMs?: number;
  qualityMetrics?: any;
  extractedTextS3Location?: {
    bucket: string;
    key: string;
  };
  imagePages?: Array<{
    pageNumber: number;
    s3Key: string;
    s3Bucket: string;
  }>;
  totalPages?: number;
  timestamp?: string;
  error?: string;
}

export class HybridDocumentProcessor {
  private static openai: OpenAI | null = null;
  private static readonly LAMBDA_URL = process.env.LAMBDA_PDF_EXTRACTOR_URL;

  /**
   * Initialize OpenAI client
   */
  private static getOpenAIClient(): OpenAI {
    if (!this.openai) {
      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error('OPENAI_API_KEY environment variable not set');
      }
      this.openai = new OpenAI({ apiKey });
    }
    return this.openai;
  }

  /**
   * Extract text from a document using hybrid approach (maintains same interface)
   */
  static async extractDocumentText(document: any): Promise<string> {
    try {
      console.log(`Extracting text from document: ${document.title}`);
      
      // If text already extracted, return it (cached)
      if (document.fileText) {
        console.log(`Using cached text for document: ${document.id}`);
        return document.fileText;
      }

      console.log(`DEBUG: About to extract from document:`, {
        id: document.id,
        title: document.title,
        fileUrl: document.fileUrl,
        hasFileText: !!document.fileText
      });

      // Extract text using new hybrid approach
      console.log(`Extracting text for document: ${document.id} using hybrid approach`);
      const result = await this.extractWithHybridApproach(document);
      
      // Log extraction details
      console.log(`Extraction complete for ${document.id}:`);
      console.log(`- Method: ${result.extractionMethod}`);
      console.log(`- Characters: ${result.characterCount}`);
      console.log(`- Words: ${result.wordCount || 'N/A'}`);
      console.log(`- Processing time: ${result.processingTimeMs || 'N/A'}ms`);
      
      // Store extracted text in database for future use
      console.log(`Storing extracted text in database (${result.extractedText.length} characters)`);
      await db
        .update(schema.documents)
        .set({ fileText: result.extractedText })
        .where(eq(schema.documents.id, document.id));

      console.log(`Text extraction completed successfully for document: ${document.id}`);
      return result.extractedText;
      
    } catch (error) {
      console.error(`Error extracting text from document ${document.id}:`, error);
      const errorMessage = `Error processing "${document.title}": ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try re-uploading the document.`;
      
      // Store the error message so we don't keep trying to extract
      await db
        .update(schema.documents)
        .set({ fileText: errorMessage })
        .where(eq(schema.documents.id, document.id));
      
      return errorMessage;
    }
  }

  /**
   * Hybrid extraction approach: Lambda first, then OpenAI Vision fallback for images
   */
  private static async extractWithHybridApproach(document: any): Promise<ExtractionResult> {
    const startTime = Date.now();
    
    try {
      // Determine file type from document title/fileUrl
      const fileExtension = this.getFileExtension(document.title || document.fileUrl);
      console.log(`Detected file type: ${fileExtension} for document: ${document.title}`);
      
      let result: Partial<ExtractionResult>;
      let extractionMethod: string;
      
      switch (fileExtension.toLowerCase()) {
        case '.txt':
          console.log('Processing as text file...');
          result = await this.extractFromTextFile(document);
          extractionMethod = 'text-file';
          break;
          
        case '.doc':
        case '.docx':
          console.log('Processing as Word document via Lambda...');
          result = await this.extractFromWordDocument(document);
          extractionMethod = 'word-doc';
          break;
          
        case '.pdf':
          console.log('Processing as PDF document with hybrid approach...');
          // First try Lambda (with enhanced unpdf + quality detection)
          try {
            const lambdaResult = await this.callLambdaExtraction(document);
            
            if (lambdaResult.success && lambdaResult.extractedText) {
              // Lambda successfully extracted text
              console.log(`Lambda extraction successful: ${lambdaResult.extractedText.length} characters`);
              result = {
                extractedText: lambdaResult.extractedText,
                characterCount: lambdaResult.characterCount || lambdaResult.extractedText.length,
                wordCount: lambdaResult.wordCount,
                extractedTextS3Location: lambdaResult.extractedTextS3Location,
                qualityMetrics: lambdaResult.qualityMetrics
              };
              extractionMethod = `lambda-${lambdaResult.extractionMethod || 'unknown'}`;
              
            } else if (lambdaResult.needsVisionProcessing && lambdaResult.imagePages) {
              // Lambda detected scanned PDF and created images - use OpenAI Vision
              console.log(`Lambda detected scanned PDF (reason: ${lambdaResult.reason}), processing ${lambdaResult.imagePages.length} images with OpenAI Vision API...`);
              result = await this.extractFromLambdaImages(lambdaResult.imagePages, document);
              result.qualityMetrics = lambdaResult.qualityMetrics;
              extractionMethod = 'openai-vision';
              
            } else {
              // Lambda failed for other reasons
              throw new Error(lambdaResult.error || 'Lambda extraction failed without clear reason');
            }
            
          } catch (lambdaError) {
            console.error('Lambda extraction failed:', lambdaError);
            throw new Error(`Document processing failed: ${lambdaError.message}`);
          }
          break;
          
        default:
          throw new Error(`Unsupported file type: ${fileExtension}. Supported formats: .txt, .doc, .docx, .pdf`);
      }
      
      if (!result.extractedText || result.extractedText.length === 0) {
        throw new Error(`No text could be extracted from ${fileExtension} file`);
      }
      
      const processingTime = Date.now() - startTime;
      return {
        extractedText: result.extractedText,
        extractionMethod: extractionMethod as any,
        characterCount: result.characterCount || result.extractedText.length,
        wordCount: result.wordCount,
        processingTimeMs: processingTime,
        extractedTextS3Location: result.extractedTextS3Location,
        qualityMetrics: result.qualityMetrics,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Hybrid extraction failed:', error);
      throw new Error(`Text extraction failed: ${error instanceof Error ? error.message : 'Unknown extraction error'}`);
    }
  }

  /**
   * Call Lambda function to extract text from document
   */
  private static async callLambdaExtraction(document: any): Promise<LambdaResponse> {
    if (!this.LAMBDA_URL) {
      throw new Error('LAMBDA_PDF_EXTRACTOR_URL environment variable not set');
    }

    console.log(`Calling Lambda for text extraction: ${document.title}`);

    // Get S3 bucket and key from FileStorageService
    const s3Location = FileStorageService.getS3Location(document.fileUrl);

    const requestBody = {
      bucket: s3Location.bucket,
      key: s3Location.key,
      documentId: document.id,
      filename: document.title
    };

    console.log(`Lambda request:`, requestBody);

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      console.log('DEBUG - LAMBDA_URL:', this.LAMBDA_URL);
      console.log('DEBUG - Request body:', JSON.stringify(requestBody, null, 2));
      
      const response = await fetch(this.LAMBDA_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Lambda API error (${response.status}): ${errorText}`);
      }

      // Parse the Lambda response structure
      const result = await response.json();
      
      // The Lambda wraps response in a body field
      let lambdaResult: LambdaResponse;
      if (result.body) {
        // Response is wrapped (API Gateway format)
        lambdaResult = JSON.parse(result.body);
      } else {
        // Direct Lambda invocation
        lambdaResult = result;
      }

      console.log(`Lambda extraction response:`, {
        success: lambdaResult.success,
        needsVisionProcessing: lambdaResult.needsVisionProcessing,
        reason: lambdaResult.reason,
        method: lambdaResult.extractionMethod,
        characters: lambdaResult.characterCount,
        words: lambdaResult.wordCount,
        processingTime: lambdaResult.processingTimeMs,
        imagePages: lambdaResult.imagePages?.length || 0
      });

      return lambdaResult;

    } catch (error) {
      console.error('Lambda extraction failed:', error);
      throw new Error(`Lambda extraction failed: ${error instanceof Error ? error.message : 'Unknown Lambda error'}`);
    }
  }

  /**
   * Extract text from Lambda-generated images using OpenAI Vision API
   */
  private static async extractFromLambdaImages(imagePages: Array<{pageNumber: number, s3Key: string, s3Bucket: string}>, document: any): Promise<Partial<ExtractionResult>> {
    try {
      const openai = this.getOpenAIClient();
      
      console.log(`Processing ${imagePages.length} pages with OpenAI Vision API...`);
      
      // Process pages in batches (OpenAI has limits)
      const batchSize = 5; // Process 5 pages at a time
      let allExtractedText = '';
      
      for (let i = 0; i < imagePages.length; i += batchSize) {
        const batch = imagePages.slice(i, i + batchSize);
        console.log(`Processing batch ${Math.floor(i/batchSize) + 1}: pages ${batch[0].pageNumber}-${batch[batch.length-1].pageNumber}`);
        
        // Download images from S3
        const imageContents = await Promise.all(
          batch.map(async (page) => {
            try {
              const imageBuffer = await FileStorageService.downloadFile(page.s3Key);
              const base64Image = imageBuffer.toString('base64');
              return {
                pageNumber: page.pageNumber,
                base64: base64Image
              };
            } catch (error) {
              console.error(`Failed to download page ${page.pageNumber}:`, error);
              return null;
            }
          })
        );
        
        // Filter out failed downloads
        const validImages = imageContents.filter(img => img !== null);
        
        if (validImages.length === 0) {
          console.warn(`No valid images in batch ${Math.floor(i/batchSize) + 1}`);
          continue;
        }
        
        // Create OpenAI Vision request
        const content = [
          {
            type: "text" as const,
            text: `Please extract all text from these ${validImages.length} pages of an educational document (IEP - Individualized Education Plan). 

Extract the text exactly as it appears, maintaining structure and formatting. Include:
- All form fields and their values
- Table contents  
- Goals and objectives
- Present levels of performance
- Accommodations and modifications
- Service delivery information

Return the text in order by page number, with clear page breaks like "=== PAGE X ===" between pages.`
          }
        ];
        
        // Add images to content
        validImages.forEach(img => {
          content.push({
            type: "image_url" as const,
            image_url: {
              url: `data:image/png;base64,${img.base64}`,
              detail: "high" as const
            }
          });
        });
        
        try {
          const response = await openai.chat.completions.create({
            model: "gpt-4o",
            messages: [
              {
                role: "user",
                content: content
              }
            ],
            max_tokens: 4000
          });

          const batchText = response.choices[0]?.message?.content || '';
          
          if (batchText.trim()) {
            allExtractedText += batchText + '\n\n';
            console.log(`Batch ${Math.floor(i/batchSize) + 1} completed: ${batchText.length} characters extracted`);
          } else {
            console.warn(`Batch ${Math.floor(i/batchSize) + 1} returned no text`);
          }
          
          // Brief delay between batches to respect rate limits
          if (i + batchSize < imagePages.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          
        } catch (visionError) {
          console.error(`Vision API failed for batch ${Math.floor(i/batchSize) + 1}:`, visionError);
          // Continue with other batches
        }
      }
      
      const cleanedText = this.cleanExtractedText(allExtractedText);
      
      if (!cleanedText || cleanedText.length < 100) {
        throw new Error('OpenAI Vision API returned insufficient text');
      }
      
      console.log(`OpenAI Vision extraction completed: ${cleanedText.length} characters from ${imagePages.length} pages`);
      
      return {
        extractedText: cleanedText,
        characterCount: cleanedText.length,
        wordCount: cleanedText.split(/\s+/).filter(word => word.length > 0).length
      };
      
    } catch (error) {
      console.error('OpenAI Vision extraction from Lambda images failed:', error);
      
      // Return helpful message when Vision API fails
      const helpfulMessage = `This document appears to be a scanned PDF with poor text quality. 
For the best results, please try to obtain a digital copy of this document from your school 
or scan it at a higher resolution (300+ DPI). 

Original error: ${error instanceof Error ? error.message : 'Unknown OpenAI error'}`;
      
      return {
        extractedText: helpfulMessage,
        characterCount: helpfulMessage.length,
        wordCount: helpfulMessage.split(/\s+/).filter(word => word.length > 0).length
      };
    }
  }

  /**
   * Get file extension from filename
   */
  private static getFileExtension(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    return ext || '';
  }

  /**
   * Extract text from plain text files
   */
  private static async extractFromTextFile(document: any): Promise<Partial<ExtractionResult>> {
    try {
      console.log(`Processing text file: ${document.title}`);
      
      // Download the file
      const fileKey = FileStorageService.getFileKeyFromUrl(document.fileUrl);
      const fileBuffer = await FileStorageService.downloadFile(fileKey);
      
      console.log(`Downloaded text file (${fileBuffer.length} bytes)`);
      
      // Convert buffer to text (assuming UTF-8 encoding)
      const extractedText = fileBuffer.toString('utf-8');
      const cleanedText = this.cleanExtractedText(extractedText);
      
      console.log(`Text file extraction successful: ${cleanedText.length} characters`);
      
      return {
        extractedText: cleanedText,
        characterCount: cleanedText.length,
        wordCount: cleanedText.split(/\s+/).filter(word => word.length > 0).length
      };
      
    } catch (error) {
      console.error('Text file extraction failed:', error);
      throw new Error(`Failed to extract text from file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract text from Word documents (.doc, .docx) using Lambda
   */
  private static async extractFromWordDocument(document: any): Promise<Partial<ExtractionResult>> {
    try {
      console.log(`Processing Word document: ${document.title} via Lambda`);
      
      // Call Lambda for Word document processing
      const lambdaResult = await this.callLambdaExtraction(document);
      
      if (lambdaResult.success && lambdaResult.extractedText) {
        console.log(`Word document extraction successful: ${lambdaResult.extractedText.length} characters`);
        
        return {
          extractedText: lambdaResult.extractedText,
          characterCount: lambdaResult.characterCount || lambdaResult.extractedText.length,
          wordCount: lambdaResult.wordCount,
          extractedTextS3Location: lambdaResult.extractedTextS3Location
        };
      } else {
        throw new Error(lambdaResult.error || 'Lambda Word document processing failed');
      }
      
    } catch (error) {
      console.error('Word document extraction failed:', error);
      throw new Error(`Failed to extract text from Word document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clean and normalize extracted text (same as original)
   */
  private static cleanExtractedText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'No text could be extracted from this document.';
    }

    return text
      .replace(/\r\n/g, '\n')          // Normalize line breaks
      .replace(/\r/g, '\n')            // Handle old Mac line breaks  
      .replace(/\n{3,}/g, '\n\n')      // Replace multiple newlines
      .replace(/[ \t]+/g, ' ')         // Replace multiple spaces
      .replace(/^\s+|\s+$/gm, '')      // Trim each line
      .trim();                         // Trim overall
  }

  /**
   * Extract text from multiple documents and combine (same interface as original)
   */
  static async extractTextFromDocuments(documents: any[]): Promise<string> {
    let combinedText = "";
    
    for (const doc of documents) {
      const extractedText = await this.extractDocumentText(doc);
      combinedText += `\n\n--- ${doc.title} ---\n${extractedText}`;
    }
    
    return combinedText;
  }

  /**
   * Health check for both Lambda and OpenAI services
   */
  static async healthCheck(): Promise<{ lambda: boolean; openai: boolean }> {
    const results = {
      lambda: false,
      openai: false
    };

    // Check Lambda
    try {
      if (this.LAMBDA_URL) {
        // Could add a health check endpoint to Lambda, for now just check URL is set
        results.lambda = true;
      }
    } catch (error) {
      console.error('Lambda health check failed:', error);
    }

    // Check OpenAI
    try {
      const openai = this.getOpenAIClient();
      await openai.models.list();
      results.openai = true;
    } catch (error) {
      console.error('OpenAI Vision health check failed:', error);
    }

    return results;
  }
}