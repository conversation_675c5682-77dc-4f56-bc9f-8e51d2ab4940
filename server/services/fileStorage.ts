import { S3<PERSON>lient, PutO<PERSON>Command, GetObjectCommand, DeleteObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import crypto from "crypto";
import path from "path";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

const bucketName = process.env.S3_BUCKET_NAME || "iep-documents-secure-2025";

export interface UploadResult {
  fileKey: string;
  fileUrl: string;
  fullPath: string;
}

export class FileStorageService {
  /**
   * Upload a file buffer to S3
   */
  static async uploadFile(file: Express.Multer.File, folder: string = "documents"): Promise<UploadResult> {
    console.log(`Processing file upload: ${file.originalname} (${file.size} bytes) to folder: ${folder}`);

    // Generate unique file key with folder structure
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(4).toString("hex");
    const fileExtension = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, fileExtension);
    
    // Create folder structure: {folder}/{timestamp}-{random}/{originalFilename}
    const folderName = `${timestamp}-${randomString}`;
    const fileName = `${baseName}${fileExtension}`;
    const fileKey = `${folder}/${folderName}/${fileName}`;

    console.log(`Uploading to S3: s3://${bucketName}/${fileKey}`);

    try {
      // Determine content type
      const contentType = this.getContentType(file.mimetype, fileExtension);

      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: fileKey,
        Body: file.buffer,
        ContentType: contentType,
        Metadata: {
          originalName: file.originalname,
          uploadTimestamp: timestamp.toString(),
        },
      });

      await s3Client.send(command);
      console.log("File uploaded successfully to S3");

      // Store the S3 key as the fileUrl (we'll generate presigned URLs when needed)
      const fileUrl = fileKey; // Store just the key, not a full URL

      return {
        fileKey,
        fileUrl,
        fullPath: `s3://${bucketName}/${fileKey}`,
      };
    } catch (error) {
      console.error("S3 upload error:", error);
      throw new Error(
        `Failed to upload to S3: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Upload an avatar file to S3 in the avatars folder
   */
  static async uploadAvatar(file: Express.Multer.File): Promise<UploadResult> {
    return this.uploadFile(file, "avatars");
  }

  /**
   * Download a file from S3
   */
  static async downloadFile(fileKey: string): Promise<Buffer> {
    console.log(`Downloading file from S3: s3://${bucketName}/${fileKey}`);

    try {
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: fileKey,
      });

      const response = await s3Client.send(command);

      if (!response.Body) {
        throw new Error("No file content received from S3");
      }

      // Convert stream to buffer
      const chunks: Uint8Array[] = [];
      const stream = response.Body as any; // AWS SDK types can be tricky
      
      if (stream.transformToByteArray) {
        // For newer AWS SDK versions
        return Buffer.from(await stream.transformToByteArray());
      } else {
        // For older versions or stream handling
        for await (const chunk of stream) {
          chunks.push(chunk);
        }
        return Buffer.concat(chunks);
      }
    } catch (error) {
      console.error("S3 download error:", error);
      
      if (error instanceof Error) {
        if (error.name === "NoSuchKey") {
          throw new Error(`File not found in S3: ${fileKey}`);
        } else if (error.name === "NoSuchBucket") {
          throw new Error(`S3 bucket not found: ${bucketName}`);
        } else if (error.name === "AccessDenied") {
          throw new Error(`Access denied to S3 object: ${fileKey}`);
        }
      }
      
      throw new Error(`Failed to download file: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Delete a file from S3
   */
  static async deleteFile(fileUrl: string): Promise<void> {
    // fileUrl is now just the S3 key
    const fileKey = this.getFileKeyFromUrl(fileUrl);
    
    console.log(`Deleting file from S3: s3://${bucketName}/${fileKey}`);

    try {
      const command = new DeleteObjectCommand({
        Bucket: bucketName,
        Key: fileKey,
      });

      await s3Client.send(command);
      console.log(`File deleted from S3: ${fileKey}`);
    } catch (error) {
      console.error(`Error deleting file from S3:`, error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Generate a presigned URL for file download
   */
  static async getPresignedDownloadUrl(fileKey: string, expiresIn: number = 3600): Promise<string> {
    console.log(`Generating presigned URL for: ${fileKey}`);

    try {
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: fileKey,
      });

      const presignedUrl = await getSignedUrl(s3Client, command, { expiresIn });
      console.log(`Generated presigned URL (expires in ${expiresIn}s)`);
      
      return presignedUrl;
    } catch (error) {
      console.error("Error generating presigned URL:", error);
      throw new Error(`Failed to generate download URL: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Serve a file from S3 (for the /api/files/:key endpoint)
   * Now returns presigned URL instead of file buffer
   */
  static async serveFile(fileKey: string): Promise<{ buffer: Buffer; contentType: string; filename: string }> {
    if (!fileKey) {
      throw new Error("File key is required");
    }

    console.log(`Serving file: ${fileKey}`);

    try {
      // Get file from S3
      const fileBuffer = await this.downloadFile(fileKey);

      // Determine content type and filename
      const fileExt = path.extname(fileKey).toLowerCase();
      const contentType = this.getContentTypeFromExtension(fileExt);
      const filename = path.basename(fileKey);

      return {
        buffer: fileBuffer,
        contentType,
        filename,
      };
    } catch (error) {
      console.error("Error serving file:", error);
      throw error; // Re-throw the error with original message
    }
  }

  /**
   * Get a presigned URL for file access (alternative to direct serving)
   */
  static async getFileAccessUrl(fileKey: string): Promise<string> {
    return this.getPresignedDownloadUrl(fileKey, 3600); // 1 hour expiration
  }

  /**
   * Extract file key from file URL (handles both old and new formats)
   */
  static getFileKeyFromUrl(fileUrl: string): string {
    // Handle old format: /api/files/12345-abcd.pdf -> documents/12345-abcd.pdf
    if (fileUrl.startsWith('/api/files/')) {
      const filename = fileUrl.replace('/api/files/', '');
      // If it's already a full key path, return as-is
      if (filename.includes('/')) {
        return filename;
      }
      // Otherwise, assume it's in the documents folder
      return `documents/${filename}`;
    }
    
    // Handle new format: already a key like "documents/12345-abcd/file.pdf"
    return fileUrl;
  }

  /**
   * Get the full S3 URL for accessing a file (for internal use)
   */
  static getS3Url(fileKey: string): string {
    return `s3://${bucketName}/${fileKey}`;
  }

  /**
   * Get bucket and key for Lambda functions
   */
  static getS3Location(fileUrl: string): { bucket: string; key: string } {
    const fileKey = this.getFileKeyFromUrl(fileUrl);
    return {
      bucket: bucketName,
      key: fileKey,
    };
  }

  /**
   * Determine content type from mimetype and extension
   */
  private static getContentType(mimetype: string, fileExtension: string): string {
    // Trust the mimetype first
    if (mimetype && mimetype !== 'application/octet-stream') {
      return mimetype;
    }

    // Fall back to extension-based detection
    return this.getContentTypeFromExtension(fileExtension);
  }

  /**
   * Get content type from file extension
   */
  private static getContentTypeFromExtension(fileExt: string): string {
    const ext = fileExt.toLowerCase();
    
    switch (ext) {
      case ".pdf":
        return "application/pdf";
      case ".doc":
        return "application/msword";
      case ".docx":
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case ".txt":
        return "text/plain";
      case ".jpg":
      case ".jpeg":
        return "image/jpeg";
      case ".png":
        return "image/png";
      default:
        return "application/octet-stream";
    }
  }

  /**
   * Health check for S3 connectivity
   */
  static async healthCheck(): Promise<boolean> {
    try {
      // Try to list objects in the bucket (with limit 1)
      const { ListObjectsV2Command } = await import("@aws-sdk/client-s3");
      const command = new ListObjectsV2Command({
        Bucket: bucketName,
        MaxKeys: 1,
      });
      
      await s3Client.send(command);
      return true;
    } catch (error) {
      console.error('S3 health check failed:', error);
      return false;
    }
  }
}