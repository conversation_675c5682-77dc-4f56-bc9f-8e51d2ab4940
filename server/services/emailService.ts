import SibApiV3Sdk from 'sib-api-v3-sdk';

// Configure Brevo API
let defaultClient = SibApiV3Sdk.ApiClient.instance;
let apiKey = defaultClient.authentications['api-key'];
apiKey.apiKey = process.env.BREVO_API_KEY;

const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

interface EmailOptions {
  to: string;
  toName?: string;
  from?: string;
  fromName?: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
}

/**
 * Send a transactional email using Brevo
 */
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    if (!process.env.BREVO_API_KEY) {
      console.error('BREVO_API_KEY not found in environment variables');
      return false;
    }

    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
    
    sendSmtpEmail.subject = options.subject;
    sendSmtpEmail.htmlContent = options.htmlContent;
    if (options.textContent) {
      sendSmtpEmail.textContent = options.textContent;
    }
    
    // Sender info - use your verified domain
    sendSmtpEmail.sender = {
      name: options.fromName || "IEPs.ai",
      email: options.from || "<EMAIL>"
    };
    
    // Recipient info
    sendSmtpEmail.to = [{
      email: options.to,
      name: options.toName || ""
    }];

    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log('Email sent successfully:', result.messageId);
    return true;
    
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

/**
 * Send welcome email to new users
 */
export async function sendWelcomeEmail(userEmail: string, userName: string): Promise<boolean> {
  const htmlContent = `
    <html>
      <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">Welcome to IEPs.ai!</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Hi ${userName},</h2>
          <p style="color: #666; line-height: 1.6;">
            Thank you for signing up for IEPs.ai! We're excited to help you manage your child's IEP journey.
          </p>
          <p style="color: #666; line-height: 1.6;">
            You can now:
          </p>
          <ul style="color: #666; line-height: 1.6;">
            <li>Upload and manage IEP documents</li>
            <li>Track your child's progress</li>
            <li>Get AI-powered insights and recommendations</li>
            <li>Communicate with your child's support team</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="https://ieps.ai/dashboard" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Get Started
            </a>
          </div>
          <p style="color: #666; line-height: 1.6; font-size: 14px;">
            If you have any questions, feel free to reach out to our support team. We may reach out to you to ask for some feedback on how we can improve.
          </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #999;">
          <p>© 2025 IEPs.ai. All rights reserved.</p>
        </div>
      </body>
    </html>
  `;

  return await sendEmail({
    to: userEmail,
    toName: userName,
    subject: "Welcome to IEPs.ai - Let's Get Started!",
    htmlContent,
    textContent: `Hi ${userName},\n\nThank you for signing up for IEPs.ai! We're excited to help you manage your child's IEP journey.\n\nYou can now upload and manage IEP documents, track your child's progress, get AI-powered insights, and communicate with your child's support team.\n\nGet started at: https://ieps.ai/dashboard\n\nIf you have any questions, feel free to reach out to our support team. We may reach out to you to ask for some feedback on how we can improve.\n\n© 2025 IEPs.ai. All rights reserved.`
  });
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(userEmail: string, userName: string, resetToken: string): Promise<boolean> {
  const resetUrl = `https://ieps.ai/reset-password?token=${resetToken}`;
  
  const htmlContent = `
    <html>
      <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">Password Reset Request</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Hi ${userName},</h2>
          <p style="color: #666; line-height: 1.6;">
            We received a request to reset your password for your IEPs.ai account.
          </p>
          <p style="color: #666; line-height: 1.6;">
            Click the button below to reset your password. This link will expire in 24 hours.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p style="color: #666; line-height: 1.6;">
            If you didn't request this password reset, please ignore this email. Your password will remain unchanged.
          </p>
          <p style="color: #999; font-size: 12px; line-height: 1.4;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            ${resetUrl}
          </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #999;">
          <p>© 2025 IEPs.ai. All rights reserved.</p>
        </div>
      </body>
    </html>
  `;

  return await sendEmail({
    to: userEmail,
    toName: userName,
    subject: "Reset Your IEPs.ai Password",
    htmlContent,
    textContent: `Hi ${userName},\n\nWe received a request to reset your password for your IEPs.ai account.\n\nClick this link to reset your password (expires in 24 hours):\n${resetUrl}\n\nIf you didn't request this password reset, please ignore this email. Your password will remain unchanged.\n\n© 2025 IEPs.ai. All rights reserved.`
  });
}

/**
 * Send contact form submission notification
 */
export async function sendContactFormEmail(formData: {
  name: string;
  email: string;
  subject: string;
  message: string;
}): Promise<boolean> {
  const htmlContent = `
    <html>
      <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">New Contact Form Submission</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Contact Details:</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #333;">Name:</td>
              <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #666;">${formData.name}</td>
            </tr>
            <tr>
              <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #333;">Email:</td>
              <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #666;">${formData.email}</td>
            </tr>
            <tr>
              <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #333;">Subject:</td>
              <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #666;">${formData.subject}</td>
            </tr>
          </table>
          <h3 style="color: #333; margin-top: 30px;">Message:</h3>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; color: #666; line-height: 1.6;">
            ${formData.message.replace(/\n/g, '<br>')}
          </div>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #999;">
          <p>© 2025 IEPs.ai Contact Form</p>
        </div>
      </body>
    </html>
  `;

  return await sendEmail({
    to: "<EMAIL>",
    toName: "IEPs.ai Support",
    from: "<EMAIL>",
    fromName: "IEPs.ai Contact Form",
    subject: `Contact Form: ${formData.subject}`,
    htmlContent,
    textContent: `New contact form submission:\n\nName: ${formData.name}\nEmail: ${formData.email}\nSubject: ${formData.subject}\n\nMessage:\n${formData.message}`
  });
}

/**
 * Send family member invitation email
 */
export async function sendFamilyInvitationEmail(
  inviterEmail: string,
  inviterName: string,
  inviteeEmail: string,
  inviteeName: string,
  invitationId: string
): Promise<boolean> {
  const invitationUrl = `https://ieps.ai/join-family?invitation=${invitationId}`;
  
  const htmlContent = `
    <html>
      <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">Family Member Invitation</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Hi ${inviteeName},</h2>
          <p style="color: #666; line-height: 1.6;">
            ${inviterName} (${inviterEmail}) has invited you to join their family workspace on IEPs.ai.
          </p>
          <p style="color: #666; line-height: 1.6;">
            By accepting this invitation, you'll be able to:
          </p>
          <ul style="color: #666; line-height: 1.6;">
            <li>View and manage IEP documents</li>
            <li>Track progress together</li>
            <li>Get AI-powered insights</li>
            <li>Collaborate on your child's education journey</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${invitationUrl}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Accept Invitation
            </a>
          </div>
          <p style="color: #666; line-height: 1.6;">
            This invitation will expire in 7 days. If you don't have an account yet, you'll be able to create one when accepting the invitation.
          </p>
          <p style="color: #999; font-size: 12px; line-height: 1.4;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            ${invitationUrl}
          </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #999;">
          <p>© 2025 IEPs.ai. All rights reserved.</p>
        </div>
      </body>
    </html>
  `;

  return await sendEmail({
    to: inviteeEmail,
    toName: inviteeName,
    subject: `${inviterName} has invited you to join their family workspace on IEPs.ai`,
    htmlContent,
    textContent: `Hi ${inviteeName},\n\n${inviterName} (${inviterEmail}) has invited you to join their family workspace on IEPs.ai.\n\nBy accepting this invitation, you'll be able to view and manage IEP documents, track progress together, get AI-powered insights, and collaborate on your child's education journey.\n\nAccept the invitation here: ${invitationUrl}\n\nThis invitation will expire in 7 days. If you don't have an account yet, you'll be able to create one when accepting the invitation.\n\n© 2025 IEPs.ai. All rights reserved.`
  });
}

/**
 * Send feedback email to support team
 */
export async function sendFeedbackEmail({
  userEmail,
  userName,
  message,
  page,
  url,
  userAgent,
  timestamp
}: {
  userEmail?: string;
  userName?: string;
  message: string;
  page: string;
  url: string;
  userAgent: string;
  timestamp: string;
}) {
  const userInfo = userEmail ? `${userName || 'Unknown'} (${userEmail})` : 'Anonymous User';
  
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>User Feedback - IEPs.ai</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">User Feedback</h1>
          <p style="color: white; margin: 10px 0 0; opacity: 0.9;">New feedback received from IEPs.ai</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px 20px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
          <div style="background: white; padding: 25px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #667eea;">
            <h2 style="color: #333; margin: 0 0 15px; font-size: 20px;">Feedback Message</h2>
            <p style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 0; white-space: pre-wrap; border: 1px solid #e9ecef;">${message}</p>
          </div>
          
          <div style="background: white; padding: 25px; border-radius: 8px; border: 1px solid #e9ecef;">
            <h3 style="color: #333; margin: 0 0 15px; font-size: 16px;">User Information</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: bold; width: 120px;">User:</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">${userInfo}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: bold;">Page:</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">${page}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: bold;">URL:</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; word-break: break-all;"><a href="${url}" style="color: #667eea;">${url}</a></td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: bold;">Timestamp:</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">${new Date(timestamp).toLocaleString()}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; vertical-align: top;">User Agent:</td>
                <td style="padding: 8px 0; word-break: break-all; font-size: 12px; color: #666;">${userAgent}</td>
              </tr>
            </table>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 12px; margin: 0;">
            © 2025 IEPs.ai. All rights reserved.
          </p>
        </div>
      </body>
    </html>
  `;

  return await sendEmail({
    to: '<EMAIL>',
    toName: 'IEPs.ai Support',
    subject: `User Feedback: ${page} - ${userInfo}`,
    htmlContent,
    textContent: `New user feedback received from IEPs.ai\n\nUser: ${userInfo}\nPage: ${page}\nURL: ${url}\nTimestamp: ${new Date(timestamp).toLocaleString()}\n\nMessage:\n${message}\n\nUser Agent: ${userAgent}`,
    fromEmail: '<EMAIL>',
    fromName: 'IEPs.ai Feedback System'
  });
}