import { db } from "../db";
import { eq } from "drizzle-orm";
import * as schema from "../../shared/schema";
import { FileStorageService } from "./fileStorage";

export interface ExtractionResult {
  extractedText: string;
  extractionMethod: 'lambda' | 'cached' | 'error';
  characterCount: number;
  wordCount?: number;
  processingTimeMs?: number;
  extractedTextS3Location?: {
    bucket: string;
    key: string;
  };
  timestamp: string;
}

export class DocumentExtractionService {
  private static readonly LAMBDA_URL = process.env.LAMBDA_PDF_EXTRACTOR_URL;
  private static readonly AWS_API_KEY = process.env.AWS_API_KEY;

  /**
   * Extract text from a document using Lambda (with caching)
   */
  static async extractDocumentText(document: any): Promise<string> {
    try {
      console.log(`Extracting text from document: ${document.title}`);
      
      // If text already extracted, return it (cached)
      if (document.fileText) {
        console.log(`Using cached text for document: ${document.id}`);
        return document.fileText;
      }

      // Extract text using enhanced Lambda
      console.log(`Extracting text for document: ${document.id} using enhanced Lambda`);
      const result = await this.callLambdaExtraction(document);
      
      // Log enhanced extraction details
      console.log(`Extraction complete for ${document.id}:`);
      console.log(`- Method: ${result.extractionMethod}`);
      console.log(`- Characters: ${result.characterCount}`);
      console.log(`- Words: ${result.wordCount || 'N/A'}`);
      console.log(`- Processing time: ${result.processingTimeMs || 'N/A'}ms`);
      if (result.extractedTextS3Location) {
        console.log(`- Saved to S3: s3://${result.extractedTextS3Location.bucket}/${result.extractedTextS3Location.key}`);
      }
      
      // Store extracted text in database for future use
      console.log(`Storing extracted text in database (${result.extractedText.length} characters)`);
      await db
        .update(schema.documents)
        .set({ fileText: result.extractedText })
        .where(eq(schema.documents.id, document.id));

      console.log(`Text extraction completed successfully for document: ${document.id}`);
      return result.extractedText;
      
    } catch (error) {
      console.error(`Error extracting text from document ${document.id}:`, error);
      const errorMessage = `Error processing "${document.title}": ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try re-uploading the document.`;
      
      // Store the error message so we don't keep trying to extract
      await db
        .update(schema.documents)
        .set({ fileText: errorMessage })
        .where(eq(schema.documents.id, document.id));
      
      return errorMessage;
    }
  }

  /**
   * Call Lambda function to extract text from document
   */
  private static async callLambdaExtraction(document: any): Promise<ExtractionResult> {
    if (!this.LAMBDA_URL) {
      throw new Error('LAMBDA_PDF_EXTRACTOR_URL environment variable not set');
    }

    console.log(`Calling Lambda for text extraction: ${document.title}`);

    // Get S3 bucket and key from FileStorageService
    const s3Location = FileStorageService.getS3Location(document.fileUrl);

    const requestBody = {
      bucket: s3Location.bucket,
      key: s3Location.key,
      documentId: document.id,
      filename: document.title
    };

    console.log(`Lambda request:`, requestBody);

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add API key if available
      // if (this.AWS_API_KEY) {
      //   headers['x-api-key'] = this.AWS_API_KEY;
      // }

      console.log('DEBUG - LAMBDA_URL:', this.LAMBDA_URL);
      console.log('DEBUG - AWS_API_KEY set:', !!this.AWS_API_KEY);
      console.log('DEBUG - Headers:', JSON.stringify(headers, null, 2));
      console.log('DEBUG - Request body:', JSON.stringify(requestBody, null, 2));
      
      const response = await fetch(this.LAMBDA_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Lambda API error (${response.status}): ${errorText}`);
      }

      // Parse the enhanced Lambda response structure
      const result = await response.json();
      
      // The enhanced Lambda wraps response in a body field
      let lambdaResult;
      if (result.body) {
        // Response is wrapped (API Gateway format)
        lambdaResult = JSON.parse(result.body);
      } else {
        // Direct Lambda invocation
        lambdaResult = result;
      }

      console.log(`Lambda extraction completed:`, {
        method: lambdaResult.extractionMethod,
        characters: lambdaResult.characterCount,
        words: lambdaResult.wordCount,
        processingTime: lambdaResult.processingTimeMs,
        success: lambdaResult.success
      });

      if (!lambdaResult.success) {
        throw new Error(lambdaResult.error || 'Lambda extraction failed');
      }

      // Clean up the extracted text
      const cleanedText = this.cleanExtractedText(lambdaResult.extractedText);

      return {
        extractedText: cleanedText,
        extractionMethod: lambdaResult.extractionMethod || 'lambda',
        characterCount: lambdaResult.characterCount || cleanedText.length,
        wordCount: lambdaResult.wordCount,
        processingTimeMs: lambdaResult.processingTimeMs,
        extractedTextS3Location: lambdaResult.extractedTextS3Location,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Lambda extraction failed:', error);
      throw new Error(`Text extraction failed: ${error instanceof Error ? error.message : 'Unknown Lambda error'}`);
    }
  }

  /**
   * Clean and normalize extracted text
   */
  private static cleanExtractedText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'No text could be extracted from this document.';
    }

    return text
      .replace(/\r\n/g, '\n')          // Normalize line breaks
      .replace(/\r/g, '\n')            // Handle old Mac line breaks  
      .replace(/\n{3,}/g, '\n\n')      // Replace multiple newlines
      .replace(/[ \t]+/g, ' ')         // Replace multiple spaces
      .replace(/^\s+|\s+$/gm, '')      // Trim each line
      .trim();                         // Trim overall
  }

  /**
   * Extract text from multiple documents and combine
   */
  static async extractTextFromDocuments(documents: any[]): Promise<string> {
    let combinedText = "";
    
    for (const doc of documents) {
      const extractedText = await this.extractDocumentText(doc);
      combinedText += `\n\n--- ${doc.title} ---\n${extractedText}`;
    }
    
    return combinedText;
  }

  /**
   * Check if Lambda service is available
   */
  static async healthCheck(): Promise<boolean> {
    if (!this.LAMBDA_URL) {
      return false;
    }

    try {
      // You could add a health check endpoint to your Lambda
      // For now, just check if the URL is configured
      return true;
    } catch (error) {
      console.error('Lambda health check failed:', error);
      return false;
    }
  }
}