import OpenAI from "openai";
import { readFileSync } from "fs";
import { join } from "path";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || "" });

// Function to load prompt from file
function loadPrompt(filename: string): string {
  try {
    const promptPath = join(process.cwd(), 'prompts', filename);
    return readFileSync(promptPath, 'utf-8').trim();
  } catch (error) {
    console.error(`Error loading prompt file ${filename}:`, error);
    return "You are a helpful AI assistant for special education and IEP documents.";
  }
}

// Function to combine default prompt with function-specific prompt
function combinePrompts(functionPrompt: string): string {
  const defaultPrompt = loadPrompt('default.md');
  return `${defaultPrompt}\n\n${functionPrompt}`;
}

// Process and analyze an IEP document
export async function analyzeDocument(documentText: string): Promise<{
  summary: string;
  goals: any[];
  accommodations: string[];
  services: string[];
}> {
  try {
    const functionPrompt = loadPrompt('document-analysis.md');
    const systemPrompt = combinePrompts(functionPrompt);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `Please analyze the following IEP document and extract the main summary, goals, accommodations, and services. Format the response as a JSON object with 'summary', 'goals', 'accommodations', and 'services' fields. For goals, include the area (e.g., 'Reading', 'Math', 'Social') and description. Here's the document text:\n\n${documentText}`
        }
      ],
      response_format: { type: "json_object" }
    });

    const result = JSON.parse(response.choices[0].message.content);
    return {
      summary: result.summary,
      goals: result.goals || [],
      accommodations: result.accommodations || [],
      services: result.services || []
    };
  } catch (error) {
    console.error("Error analyzing IEP document:", error);
    throw new Error("Failed to analyze IEP document. Please try again.");
  }
}

// Generate a summary of an IEP document
export async function generateDocumentSummary(documentText: string): Promise<string> {
  try {
    const functionPrompt = loadPrompt('document-summary.md');
    const systemPrompt = combinePrompts(functionPrompt);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `Please provide a concise summary of the following IEP document, focusing on the key points that a parent should understand:\n\n${documentText}`
        }
      ]
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error("Error generating document summary:", error);
    throw new Error("Failed to generate document summary. Please try again.");
  }
}

// Extract and analyze IEP goals from a document
export async function analyzeIEPGoals(documentText: string): Promise<any> {
  try {
    const functionPrompt = loadPrompt('goal-analysis.md');
    const systemPrompt = combinePrompts(functionPrompt);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `Please extract and analyze all IEP goals from the following document. Return a JSON object with a 'goals' array containing objects with 'area', 'description', and 'measurableOutcomes' fields:\n\n${documentText}`
        }
      ],
      response_format: { type: "json_object" }
    });

    return JSON.parse(response.choices[0].message.content);
  } catch (error) {
    console.error("Error analyzing IEP goals:", error);
    throw new Error("Failed to analyze IEP goals. Please try again.");
  }
}

// Generate suggested questions based on an IEP document
export async function generateSuggestedQuestions(documentText: string): Promise<string[]> {
  try {
    const functionPrompt = loadPrompt('suggested-questions.md');
    const systemPrompt = combinePrompts(functionPrompt);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `Based on the following IEP document, generate 5-7 specific, relevant questions that a parent might want to ask to better understand their child's IEP or to prepare for an IEP meeting. Return the questions as a JSON array of strings:\n\n${documentText}`
        }
      ],
      response_format: { type: "json_object" }
    });

    const result = JSON.parse(response.choices[0].message.content);
    return result.questions || [];
  } catch (error) {
    console.error("Error generating suggested questions:", error);
    throw new Error("Failed to generate suggested questions. Please try again.");
  }
}

// Answer a question about an IEP document
export async function answerQuestion(question: string, documentText: string): Promise<{
  answer: string;
  sourceDocuments?: Array<{ name: string; pageNumber: number }>;
}> {
  try {
    const functionPrompt = loadPrompt('question-answering.md');
    const systemPrompt = combinePrompts(functionPrompt);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `I have a question about my child's IEP document. Here's the document text for context:\n\n${documentText}\n\nMy question is: ${question}`
        }
      ]
    });

    return {
      answer: response.choices[0].message.content,
      // For now, we're not implementing page number tracking
      sourceDocuments: []
    };
  } catch (error) {
    console.error("Error answering question:", error);
    throw new Error("Failed to answer question. Please try again.");
  }
}

// Answer a question about an IEP document WITH streaming
export async function answerQuestionStream(
  question: string, 
  documentText: string, 
  onChunk: (chunk: string) => void,
  onComplete: () => void,
  onError: (error: Error) => void
): Promise<void> {
  try {
    const functionPrompt = loadPrompt('question-answering.md');
    const systemPrompt = combinePrompts(functionPrompt);

    const stream = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `I have a question about my child's IEP document. Here's the document text for context:\n\n${documentText}\n\nMy question is: ${question}`
        }
      ],
      stream: true, // Enable streaming
    });

    let fullResponse = '';
    
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        fullResponse += content;
        onChunk(content); // Send each token to the callback
      }
    }
    
    onComplete();
  } catch (error) {
    console.error("Error in streaming answer:", error);
    onError(error instanceof Error ? error : new Error("Failed to generate streaming response"));
  }
}