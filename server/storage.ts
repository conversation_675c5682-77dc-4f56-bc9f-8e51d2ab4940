import {
  users,
  type User,
  type UpsertUser,
  children,
  type Child,
  type InsertChild,
  documents,
  type Document,
  type InsertDocument,
  familyMembers,
  type FamilyMember,
  type InsertFamilyMember,
  familyInvitations,
  type FamilyInvitation,
  type InsertFamilyInvitation,
  conversations,
  type Conversation,
  type InsertConversation,
  messages,
  type Message,
  type InsertMessage
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, sql } from "drizzle-orm"; // FIXED: Import sql from drizzle-orm
import { FileStorageService } from "./services/fileStorage";

// Interface for storage operations
export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  
  // Child operations
  getChildrenByUserId(userId: string): Promise<Child[]>;
  getChildById(id: string): Promise<Child | undefined>;
  createChild(userId: string, child: { name: string, birthMonth?: string, birthYear?: string, avatarUrl?: string }): Promise<Child>;
  userHasAccessToChild(userId: string, childId: string): Promise<boolean>;
  updateDocumentChildId(documentId: string, childId: string): Promise<Document>;
  
  // Document operations
  getDocumentsByChildId(childId: string): Promise<Document[]>;
  getDocumentById(id: string): Promise<Document | undefined>;
  createDocument(document: InsertDocument): Promise<Document>;
  
  // Family member operations
  getFamilyMembersByUserId(userId: string): Promise<FamilyMember[]>;
  createFamilyMember(familyMember: InsertFamilyMember): Promise<FamilyMember>;
  createFamilyInvitation(userId: string, email: string): Promise<FamilyInvitation>;
  getFamilyInvitation(invitationId: string): Promise<FamilyInvitation | undefined>;
  getFamilyInvitationsByUserId(userId: string): Promise<FamilyInvitation[]>;
  updateFamilyInvitation(invitationId: string, updates: Partial<FamilyInvitation>): Promise<FamilyInvitation>;
  deleteFamilyInvitation(invitationId: string): Promise<void>;
  
  // Conversation operations
  getConversationsByChildId(childId: string): Promise<Conversation[]>;
  getConversationById(id: string): Promise<Conversation | undefined>;
  createConversation(conversation: InsertConversation): Promise<Conversation>;
  updateConversation(id: string, updates: Partial<Conversation>): Promise<Conversation>;
  
  // Message operations
  getMessagesByConversationId(conversationId: string): Promise<Message[]>;
  createMessage(message: InsertMessage): Promise<Message>;

  deleteChildAndRelatedData(childId: string): Promise<void>;

  // New methods
  getConversationByChildId(childId: string): Promise<Conversation | undefined>;
  updateConversationSummary(id: string, summary: string, documentCount: number): Promise<Conversation>;
  createConversationWithSummary(childId: string, userId: string, summary: string, documentCount: number): Promise<Conversation>;

  updateUserProfile(userId: string, updates: { firstName?: string; lastName?: string }): Promise<User | null>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      return user;
    } catch (error) {
      console.error("Error getting user:", error);
      throw error;
    }
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    try {
      const [user] = await db
        .insert(users)
        .values(userData)
        .onConflictDoUpdate({
          target: users.id,
          set: {
            ...userData,
            updatedAt: new Date(),
          },
        })
        .returning();
      return user;
    } catch (error) {
      console.error("Error upserting user:", error);
      throw error;
    }
  }

  async updateUserProfile(userId: string, updates: { firstName?: string; lastName?: string }): Promise<User | null> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set({
          ...updates,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId))
        .returning();
      
      return updatedUser || null;
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  }
  
  // Child operations
  async getChildrenByUserId(userId: string): Promise<Child[]> {
    console.log("[DEBUG] storage.getChildrenByUserId called with userId:", userId);
    
    try {
      // Get children created by the user
      const userChildren = await db.select().from(children).where(eq(children.createdBy, userId));
      console.log("[DEBUG] User's own children:", userChildren);
      
      // Get children shared with the user via family members
      const sharedChildren = await db
        .select({
          child: children
        })
        .from(familyMembers)
        .innerJoin(children, eq(familyMembers.userId, children.createdBy))
        .where(eq(familyMembers.familyMemberId, userId));
      
      console.log("[DEBUG] Shared children:", sharedChildren);
      
      // Combine both sets
      const allChildren = [
        ...userChildren,
        ...sharedChildren.map(item => item.child)
      ];
      
      console.log("[DEBUG] All children combined:", allChildren);
      return allChildren;
    } catch (error) {
      console.error("[DEBUG] Error in storage.getChildrenByUserId:", error);
      throw error;
    }
  }

  async getChildById(id: string): Promise<Child | undefined> {
    try {
      const [child] = await db.select().from(children).where(eq(children.id, id));
      return child;
    } catch (error) {
      console.error("Error getting child by ID:", error);
      throw error;
    }
  }

  async createChild(userId: string, child: { 
    name: string, 
    birthMonth?: string, 
    birthYear?: string, 
    avatarUrl?: string 
  }): Promise<Child> {
    console.log("[DEBUG] storage.createChild called with:", { userId, child });
    
    try {
      const childValues = {
        name: child.name,
        birthMonth: child.birthMonth || null,
        birthYear: child.birthYear || null,
        avatarUrl: child.avatarUrl || null,
        createdBy: userId
      };
      
      console.log("[DEBUG] Inserting child with values:", childValues);
      
      const [newChild] = await db
        .insert(children)
        .values(childValues)
        .returning();
      
      console.log("[DEBUG] Child inserted successfully:", newChild);
      return newChild;
    } catch (error) {
      console.error("[DEBUG] Error in storage.createChild:", error);
      throw error;
    }
  }
  
  async updateDocumentChildId(documentId: string, childId: string): Promise<Document> {
    try {
      const [updatedDocument] = await db
        .update(documents)
        .set({ childId })
        .where(eq(documents.id, documentId))
        .returning();
      return updatedDocument;
    } catch (error) {
      console.error("Error updating document child ID:", error);
      throw error;
    }
  }

  async userHasAccessToChild(userId: string, childId: string): Promise<boolean> {
    try {
      // Check if user created the child
      const [child] = await db
        .select()
        .from(children)
        .where(and(
          eq(children.id, childId),
          eq(children.createdBy, userId)
        ));
      
      if (child) return true;
      
      // Check if user has access via family sharing
      const [childCreator] = await db
        .select()
        .from(children)
        .where(eq(children.id, childId));
      
      if (!childCreator) return false;
      
      const [familyMember] = await db
        .select()
        .from(familyMembers)
        .where(and(
          eq(familyMembers.userId, childCreator.createdBy),
          eq(familyMembers.familyMemberId, userId)
        ));
      
      return !!familyMember;
    } catch (error) {
      console.error("Error checking user access to child:", error);
      return false;
    }
  }
  
  // Document operations
  async getDocumentsByChildId(childId: string): Promise<Document[]> {
    try {
      return await db
        .select()
        .from(documents)
        .where(eq(documents.childId, childId))
        .orderBy(desc(documents.createdAt));
    } catch (error) {
      console.error("Error getting documents by child ID:", error);
      throw error;
    }
  }

  async getDocumentById(id: string): Promise<Document | undefined> {
    try {
      const [document] = await db.select().from(documents).where(eq(documents.id, id));
      
      if (document) {
        // Get child name for context
        const [child] = await db.select().from(children).where(eq(children.id, document.childId));
        if (child) {
          // Add childName as a non-persisted property in the runtime object
          const documentWithExtra = document as Document & { childName?: string };
          documentWithExtra.childName = child.name;
          return documentWithExtra;
        }
      }
      
      return document;
    } catch (error) {
      console.error("Error getting document by ID:", error);
      throw error;
    }
  }

  async createDocument(document: InsertDocument): Promise<Document> {
    console.log("[STORAGE] Starting createDocument with data:", document);
    try {
      console.log("[STORAGE] Inserting document into database...");
      const [newDocument] = await db
        .insert(documents)
        .values(document)
        .returning();
      console.log("[STORAGE] Document created successfully:", newDocument);
      return newDocument;
    } catch (error) {
      console.log("[STORAGE] ERROR creating document:", error);
      console.log("[STORAGE] Error details:", {
        message: (error as Error).message,
        stack: (error as Error).stack
      });
      throw error;
    }
  }
  
  // Family member operations
  async getFamilyMembersByUserId(userId: string): Promise<FamilyMember[]> {
    try {
      const familyMembersList = await db.select().from(familyMembers).where(eq(familyMembers.userId, userId));
      
      // Enhance each family member with user details
      const enhancedFamilyMembers = await Promise.all(
        familyMembersList.map(async (familyMember) => {
          const [user] = await db.select().from(users).where(eq(users.id, familyMember.familyMemberId));
          
          // Add user details as non-persisted properties
          const enhancedFamilyMember = familyMember as FamilyMember & {
            firstName?: string;
            lastName?: string;
            email?: string;
            profileImageUrl?: string;
          };
          
          if (user) {
            enhancedFamilyMember.firstName = user.firstName;
            enhancedFamilyMember.lastName = user.lastName;
            enhancedFamilyMember.email = user.email;
            enhancedFamilyMember.profileImageUrl = user.profileImageUrl;
          }
          
          return enhancedFamilyMember;
        })
      );
      
      return enhancedFamilyMembers;
    } catch (error) {
      console.error("Error getting family members:", error);
      throw error;
    }
  }

  async createFamilyMember(familyMember: InsertFamilyMember): Promise<FamilyMember> {
    try {
      console.log("[DEBUG] storage.createFamilyMember called with:", familyMember);
      const [newFamilyMember] = await db
        .insert(familyMembers)
        .values(familyMember)
        .returning();
      console.log("[DEBUG] Family member created successfully:", newFamilyMember);
      return newFamilyMember;
    } catch (error) {
      console.error("[DEBUG] Error creating family member:", error);
      throw error;
    }
  }

  async createFamilyInvitation(userId: string, email: string): Promise<FamilyInvitation> {
    try {
      const [invitation] = await db
        .insert(familyInvitations)
        .values({
          userId,
          email,
          status: 'pending'
        })
        .returning();
      return invitation;
    } catch (error) {
      console.error("Error creating family invitation:", error);
      throw error;
    }
  }

  async getFamilyInvitation(invitationId: string): Promise<FamilyInvitation | undefined> {
    try {
      const [invitation] = await db
        .select()
        .from(familyInvitations)
        .where(eq(familyInvitations.id, invitationId));
      
      if (invitation) {
        // Get inviter information
        const [inviter] = await db
          .select()
          .from(users)
          .where(eq(users.id, invitation.userId));
        
        // Add inviter name as a non-persisted property
        const invitationWithExtra = invitation as FamilyInvitation & { inviterName?: string };
        if (inviter) {
          invitationWithExtra.inviterName = `${inviter.firstName} ${inviter.lastName}`.trim();
        }
        return invitationWithExtra;
      }
      
      return invitation;
    } catch (error) {
      console.error("Error getting family invitation:", error);
      throw error;
    }
  }

  async getFamilyInvitationsByUserId(userId: string): Promise<FamilyInvitation[]> {
    try {
      console.log("[DEBUG] Getting family invitations for userId:", userId);
      const invitations = await db
        .select()
        .from(familyInvitations)
        .where(eq(familyInvitations.userId, userId));
      
      console.log("[DEBUG] Found family invitations:", invitations);
      
      // Also check for invitations TO this user (by email)
      const [user] = await db.select().from(users).where(eq(users.id, userId));
      if (user) {
        const invitationsToUser = await db
          .select()
          .from(familyInvitations)
          .where(eq(familyInvitations.email, user.email));
        console.log("[DEBUG] Found invitations TO this user:", invitationsToUser);
      }
      
      return invitations;
    } catch (error) {
      console.error("Error getting family invitations by user ID:", error);
      throw error;
    }
  }

  async updateFamilyInvitation(invitationId: string, updates: Partial<FamilyInvitation>): Promise<FamilyInvitation> {
    try {
      const [updatedInvitation] = await db
        .update(familyInvitations)
        .set({
          ...updates,
          respondedAt: new Date()
        })
        .where(eq(familyInvitations.id, invitationId))
        .returning();
      return updatedInvitation;
    } catch (error) {
      console.error("Error updating family invitation:", error);
      throw error;
    }
  }

  async deleteFamilyInvitation(invitationId: string): Promise<void> {
    try {
      await db
        .delete(familyInvitations)
        .where(eq(familyInvitations.id, invitationId));
    } catch (error) {
      console.error("Error deleting family invitation:", error);
      throw error;
    }
  }
  
  // Conversation operations
  async getConversationsByChildId(childId: string): Promise<Conversation[]> {
    try {
      console.log("Fetching conversations for child:", childId);
      
      // Updated query to include new hasSummary and documentCountAtSummary fields
      const conversationList = await db
        .select()
        .from(conversations)
        .where(eq(conversations.childId, childId))
        .orderBy(desc(conversations.createdAt));
      
      console.log(`Found ${conversationList.length} conversations for child ${childId}`);
      
      // Enhance each conversation with message data using separate queries
      const enhancedConversations = await Promise.all(
        conversationList.map(async (conversation) => {
          const enhancedConversation = conversation as Conversation & { 
            previewText?: string;
            messageCount?: number;
            lastMessageAt?: Date;
          };
          
          // Get message count using a separate simple query
          const messageCountResult = await db
            .select({ count: sql<number>`cast(count(*) as int)` })
            .from(messages)
            .where(eq(messages.conversationId, conversation.id));
          
          enhancedConversation.messageCount = messageCountResult[0]?.count || 0;
          
          // Get latest message for preview text and timestamp
          const [latestMessage] = await db
            .select()
            .from(messages)
            .where(eq(messages.conversationId, conversation.id))
            .orderBy(desc(messages.timestamp))
            .limit(1);
          
          if (latestMessage) {
            enhancedConversation.previewText = latestMessage.content.substring(0, 100);
            enhancedConversation.lastMessageAt = latestMessage.timestamp;
          }
          
          console.log(`Conversation ${conversation.id}: hasSummary=${conversation.hasSummary}, documentCountAtSummary=${conversation.documentCountAtSummary}`);
          
          return enhancedConversation;
        })
      );
      
      console.log(`Enhanced ${enhancedConversations.length} conversations with message data`);
      return enhancedConversations;
    } catch (error) {
      console.error("Error in getConversationsByChildId:", error);
      throw error;
    }
  }

  async getConversationById(id: string): Promise<Conversation | undefined> {
    try {
      console.log("Fetching conversation by ID:", id);
      const [conversation] = await db.select().from(conversations).where(eq(conversations.id, id));
      
      if (!conversation) {
        console.log("Conversation not found:", id);
        return undefined;
      }
      
      console.log(`Conversation found: ${conversation.id}, hasSummary=${conversation.hasSummary}, documentCountAtSummary=${conversation.documentCountAtSummary}`);
      return conversation;
    } catch (error) {
      console.error("Error in getConversationById:", error);
      throw error;
    }
  }

  async createConversation(conversation: InsertConversation): Promise<Conversation> {
    try {
      console.log("Creating conversation with data:", conversation);
      
      // Ensure the new fields have default values if not provided
      const conversationData = {
        ...conversation,
        hasSummary: conversation.hasSummary ?? false,
        documentCountAtSummary: conversation.documentCountAtSummary ?? 0
      };
      
      console.log("Final conversation data to insert:", conversationData);
      
      const [newConversation] = await db
        .insert(conversations)
        .values(conversationData)
        .returning();
      
      console.log(`Conversation created successfully: ${newConversation.id}, hasSummary=${newConversation.hasSummary}, documentCountAtSummary=${newConversation.documentCountAtSummary}`);
      return newConversation;
    } catch (error) {
      console.error("Error in createConversation:", error);
      throw error;
    }
  }

  async updateConversation(id: string, updates: Partial<Conversation>): Promise<Conversation> {
    try {
      console.log("Updating conversation:", id, "with updates:", updates);
      
      const [updatedConversation] = await db
        .update(conversations)
        .set(updates)
        .where(eq(conversations.id, id))
        .returning();
      
      console.log(`Conversation updated successfully: ${updatedConversation.id}, hasSummary=${updatedConversation.hasSummary}, documentCountAtSummary=${updatedConversation.documentCountAtSummary}`);
      return updatedConversation;
    } catch (error) {
      console.error("Error in updateConversation:", error);
      throw error;
    }
  }
  
  // Message operations
  async getMessagesByConversationId(conversationId: string): Promise<Message[]> {
    try {
      console.log("Fetching messages for conversation:", conversationId);
      const messageList = await db
        .select()
        .from(messages)
        .where(eq(messages.conversationId, conversationId))
        .orderBy(messages.timestamp); // Order by timestamp ascending for chronological order
      
      console.log(`Found ${messageList.length} messages for conversation ${conversationId}`);
      return messageList;
    } catch (error) {
      console.error("Error in getMessagesByConversationId:", error);
      throw error;
    }
  }

  // FIXED: createMessage method - only uses fields that exist in schema
  async createMessage(message: InsertMessage): Promise<Message> {
    try {
      console.log("Creating message for conversation:", message.conversationId);
      const [newMessage] = await db
        .insert(messages)
        .values({
          id: message.id,
          conversationId: message.conversationId,
          role: message.role,
          content: message.content,
          timestamp: message.timestamp,
          // REMOVED: createdAt and updatedAt (don't exist in schema)
        })
        .returning();
      
      // REMOVED: Update conversation updatedAt (field doesn't exist in schema)
      // This was causing the "syntax error at or near 'where'" error
      
      console.log("Message created successfully:", newMessage.id);
      return newMessage;
    } catch (error) {
      console.error("Error in createMessage:", error);
      throw error;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
      return result[0] || null;
    } catch (error) {
      console.error("Error getting user by email:", error);
      throw error;
    }
  }

  async deleteChildAndRelatedData(childId: string): Promise<void> {
    try {
      // Use a transaction to ensure atomicity
      await db.transaction(async (tx) => {
        // 1. Get all documents for this child to handle S3 files later
        const childDocuments = await tx
          .select()
          .from(documents)
          .where(eq(documents.childId, childId));
  
        // 2. Get all conversations for this child first
        const childConversations = await tx
          .select({ id: conversations.id })
          .from(conversations)
          .where(eq(conversations.childId, childId));
  
        // 3. Delete all messages in these conversations
        if (childConversations.length > 0) {
          const conversationIds = childConversations.map(c => c.id);
          for (const conversationId of conversationIds) {
            await tx
              .delete(messages)
              .where(eq(messages.conversationId, conversationId));
          }
        }
  
        // 4. Delete all conversations for this child
        await tx
          .delete(conversations)
          .where(eq(conversations.childId, childId));
  
        // 5. Delete all documents for this child
        await tx
          .delete(documents)
          .where(eq(documents.childId, childId));
  
        // 6. Delete the child record
        await tx
          .delete(children)
          .where(eq(children.id, childId));
  
        // 7. Delete S3 files (after successful DB transaction)
        if (childDocuments.length > 0) {
          await Promise.all(
            childDocuments.map((doc) => FileStorageService.deleteFile(doc.fileUrl))
          );
        }
      });
    } catch (error) {
      console.error("Error in deleteChildAndRelatedData:", error);
      throw error;
    }
  }

  // Get the first conversation for a child (for summary storage)
  async getConversationByChildId(childId: string): Promise<Conversation | undefined> {
    try {
      console.log("Getting first conversation for child:", childId);
      const [conversation] = await db
        .select()
        .from(conversations)
        .where(eq(conversations.childId, childId))
        .orderBy(desc(conversations.createdAt))
        .limit(1);
      
      return conversation;
    } catch (error) {
      console.error("Error in getConversationByChildId:", error);
      throw error;
    }
  }

  // Update conversation with summary
  async updateConversationSummary(id: string, summary: string, documentCount: number): Promise<Conversation> {
    try {
      console.log("Updating conversation summary:", id);
      
      const [updatedConversation] = await db
        .update(conversations)
        .set({
          summary,
          hasSummary: true,
          documentCountAtSummary: documentCount
        })
        .where(eq(conversations.id, id))
        .returning();
      
      console.log(`Conversation summary updated successfully: ${updatedConversation.id}`);
      return updatedConversation;
    } catch (error) {
      console.error("Error in updateConversationSummary:", error);
      throw error;
    }
  }

  // Create conversation with summary
  async createConversationWithSummary(childId: string, userId: string, summary: string, documentCount: number): Promise<Conversation> {
    try {
      console.log("Creating conversation with summary for child:", childId);
      
      const [newConversation] = await db
        .insert(conversations)
        .values({
          childId,
          title: "Document Analysis",
          createdBy: userId,
          summary,
          hasSummary: true,
          documentCountAtSummary: documentCount,
          createdAt: new Date()
        })
        .returning();
      
      console.log(`Conversation with summary created successfully: ${newConversation.id}`);
      return newConversation;
    } catch (error) {
      console.error("Error in createConversationWithSummary:", error);
      throw error;
    }
  }
}

export const storage = new DatabaseStorage();