import { Request, Response } from 'express';
import { db } from './db';
import { documents } from '../shared/schema';
import busboy from 'busboy';
import { Client } from '@replit/object-storage';
import path from 'path';
import crypto from 'crypto';

// Create object storage client
const storage = new Client();
const bucketName = 'iep-documents';

/**
 * Simple file upload handler using Replit Object Storage
 */
export async function handleBasicFileUpload(req: Request, res: Response) {
  try {
    // Get user info from request
    const userId = (req.user as any)?.id || 'anonymous';
    const childId = req.params.childId;
    
    console.log(`Starting file upload for child ${childId} by user ${userId}`);
    
    // Initialize busboy with limits
    const bb = busboy({
      headers: req.headers,
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB size limit
        files: 1 // Only allow one file per upload
      }
    });
    
    let fileFound = false;
    let uploadError: string | null = null;
    
    // Process file field
    bb.on('file', (fieldname, fileStream, info) => {
      fileFound = true;
      const { filename, mimeType } = info;
      
      console.log(`Processing file: ${filename} (${mimeType})`);
      
      // Collect file data in chunks
      const fileChunks: Buffer[] = [];
      let fileSize = 0;
      
      fileStream.on('data', (chunk) => {
        fileChunks.push(chunk);
        fileSize += chunk.length;
        console.log(`Received chunk: ${chunk.length} bytes, total: ${fileSize} bytes`);
      });
      
      fileStream.on('limit', () => {
        uploadError = 'File size limit exceeded (max 50MB)';
        console.error('File size limit exceeded');
        fileStream.resume(); // Drain stream
      });
      
      fileStream.on('end', async () => {
        // Skip if we already encountered an error
        if (uploadError) return;
        
        try {
          console.log(`File upload complete. Total size: ${fileSize} bytes`);
          
          // Generate file key (unique identifier)
          const timestamp = Date.now();
          const randomString = crypto.randomBytes(4).toString('hex');
          const fileExtension = path.extname(filename);
          const fileKey = `${timestamp}-${randomString}${fileExtension}`;
          const fullPath = `${bucketName}/${fileKey}`;
          
          // Create a Buffer from all chunks
          const fileBuffer = Buffer.concat(fileChunks);
          
          console.log(`Uploading to Object Storage: ${fullPath}`);
          
          // Upload to Object Storage
          const uploadResult = await storage.uploadFromBytes(fullPath, fileBuffer);
          
          if (!uploadResult.ok) {
            throw new Error(`Failed to upload to Object Storage: ${uploadResult.error.message}`);
          }
          
          console.log('File uploaded successfully to Object Storage');
          
          // Generate a URL for accessing the file via our API
          const fileUrl = `/api/files/${fileKey}`;
          
          // Store document metadata in database
          console.log(`Storing document metadata in database for child ${childId}`);
          
          const documentId = crypto.randomUUID();
          const [document] = await db.insert(documents).values({
            id: documentId,
            childId,
            title: filename,
            documentType: 'Other',
            fileUrl,
            uploadedBy: userId
          }).returning();
          
          console.log(`Document saved with ID: ${document.id}`);
          
          // Send success response
          res.status(201).json({
            success: true,
            document: {
              id: document.id,
              title: document.title,
              fileUrl: document.fileUrl
            }
          });
        } catch (err: any) {
          console.error('Error processing file:', err);
          
          // Only send response if not already sent
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: `Upload error: ${err.message}`
            });
          }
        }
      });
    });
    
    // Handle no file uploaded
    bb.on('finish', () => {
      if (!fileFound && !res.headersSent) {
        res.status(400).json({
          success: false,
          message: 'No file was uploaded'
        });
      }
      
      if (uploadError && !res.headersSent) {
        res.status(400).json({
          success: false,
          message: uploadError
        });
      }
    });
    
    // Handle any parsing errors
    bb.on('error', (err) => {
      console.error('Busboy error:', err);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: `Upload error: ${err instanceof Error ? err.message : 'Unknown error'}`
        });
      }
    });
    
    // Start parsing by piping the request to busboy
    req.pipe(bb);
  } catch (err: any) {
    console.error('Upload handler error:', err);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: `Server error: ${err.message}`
      });
    }
  }
}

/**
 * Handler for serving files from Object Storage
 */
export async function serveBasicFile(req: Request, res: Response) {
  try {
    const fileKey = req.params.key;
    
    if (!fileKey) {
      return res.status(400).send('File key is required');
    }
    
    console.log(`Serving file: ${fileKey}`);
    
    // Full path in the bucket
    const fullPath = `${bucketName}/${fileKey}`;
    
    // Get file from Object Storage
    const result = await storage.downloadAsBytes(fullPath);
    
    if (!result.ok) {
      throw new Error(`File not found: ${result.error.message}`);
    }
    
    // Determine content type based on file extension
    const fileExt = path.extname(fileKey).toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (fileExt) {
      case '.pdf': contentType = 'application/pdf'; break;
      case '.doc': contentType = 'application/msword'; break;
      case '.docx': contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; break;
      case '.txt': contentType = 'text/plain'; break;
      case '.jpg':
      case '.jpeg': contentType = 'image/jpeg'; break;
      case '.png': contentType = 'image/png'; break;
    }
    
    // Set headers and send the file
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `inline; filename="${path.basename(fileKey)}"`);
    res.send(result.value);
  } catch (err: any) {
    console.error('Error serving file:', err);
    res.status(404).send(`File not found or error: ${err.message}`);
  }
}