import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

console.log("[DEBUG] Initializing database connection with URL:", 
  process.env.DATABASE_URL.substring(0, 20) + "..." // Only log part of the URL for security
);

export const pool = new Pool({ connectionString: process.env.DATABASE_URL });

// Test the connection
pool.query('SELECT NOW()')
  .then(result => console.log("[DEBUG] Database connection successful:", result.rows[0]))
  .catch(err => console.error("[DEBUG] Database connection error:", err));

export const db = drizzle({ client: pool, schema });
