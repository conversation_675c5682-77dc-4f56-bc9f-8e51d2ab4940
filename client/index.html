<!doctype html>
<html lang="en">
  <head>
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1"
    />
    <meta
      name="description"
      content="IEPs.ai helps parents navigate Individualized Education Plans with confidence and clarity, providing personalized insights through AI-powered document analysis."
    />
    <meta
      property="og:title"
      content="IEPs.ai - Navigate IEPs with Confidence"
    />
    <meta
      property="og:description"
      content="IEPs.ai helps parents decode complex Individualized Education Plans, providing personalized insights and guidance."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ieps.ai" />
    <meta property="og:image" content="https://ieps.ai/images/ieps-ai-logo.png" />
    <title>IEPs.ai - Navigate IEPs with Confidence</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;600&display=swap"
      rel="stylesheet"
    />
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GX2EK591JW"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      
      // Default to 'denied' until user consents
      gtag('consent', 'default', {
        'analytics_storage': 'denied'
      });

      // Initialize GA with your measurement ID
      gtag('config', 'G-GX2EK591JW');

      // Check for existing consent
      const consent = localStorage.getItem('cookie-consent');
      if (consent === 'accepted') {
        gtag('consent', 'update', {
          'analytics_storage': 'granted'
        });
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script
      type="text/javascript"
      src="https://replit.com/public/js/replit-dev-banner.js"
    ></script>
  </body>
</html>
