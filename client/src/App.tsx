import { useState, useEffect } from "react";
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import HomePage from "@/pages/home";
import Dashboard from "@/pages/dashboard";
import ChildProfile from "@/pages/child-profile";
import DocumentViewer from "@/pages/document-viewer";
import DocumentUpload from "@/pages/document-upload";
import AIAssistant from "@/pages/ai-assistant";
import ConversationPage from "@/pages/conversation";
import TestAddChild from "@/pages/test-add-child";
import ResetPassword from "@/pages/reset-password";
import LoginPage from "@/pages/login";
import SignupPage from "@/pages/signup";
import ComingSoon from "@/pages/coming-soon";
import Roadmap from "@/pages/roadmap";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfUse from "@/pages/terms-of-use";
import ContactPage from "@/pages/contact-form";
import BetaPage from "@/pages/beta";
import JoinFamily from "@/pages/join-family";
import CookieConsent from "@/components/ui/cookie-consent";

function Router() {
  return (
    <Switch>
      <Route path="/" component={HomePage} />
      <Route path="/dashboard" component={Dashboard} />
      <Route path="/child-profile/:id" component={ChildProfile} />
      <Route path="/document-viewer" component={DocumentViewer} />
      <Route path="/document-viewer/:id" component={DocumentViewer} />
      <Route path="/document-upload/:childId" component={DocumentUpload} />
      <Route path="/conversation/:childId" component={ConversationPage} />
      <Route path="/test-add-child" component={TestAddChild} />
      <Route path="/reset-password" component={ResetPassword} />
      <Route path="/coming-soon" component={ComingSoon} />
      <Route path="/roadmap" component={Roadmap} />
      <Route path="/privacy-policy" component={PrivacyPolicy} />
      <Route path="/terms-of-use" component={TermsOfUse} />
      <Route path="/contact" component={ContactPage} />
      <Route path="/beta" component={BetaPage} />
      <Route path="/join-family" component={JoinFamily} />
      <Route path="/login" component={LoginPage} />
      <Route path="/signup" component={SignupPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
        <CookieConsent />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;