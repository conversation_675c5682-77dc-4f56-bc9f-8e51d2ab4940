import { apiRequest } from "./queryClient";

// Interface for the AI assistant response
export interface AIResponse {
  answer: string;
  sourceDocuments?: Array<{
    name: string;
    pageNumber: number;
  }>;
}

// Function to ask the AI assistant a question about an IEP document
export async function askAIAssistant(childId: string, question: string): Promise<AIResponse> {
  try {
    const response = await apiRequest(
      "POST", 
      `/api/ai/ask/${childId}`, 
      { question }
    );
    
    return await response.json();
  } catch (error) {
    console.error("Error asking AI assistant:", error);
    throw new Error("Failed to get answer from AI assistant. Please try again.");
  }
}

// Function to get a summary of an IEP document
export async function getDocumentSummary(documentId: string, childId?: string): Promise<AIResponse> {
  try {
    const url = childId 
      ? `/api/ai/summary/${documentId}?childId=${childId}`
      : `/api/ai/summary/${documentId}`;
      
    console.log("Requesting document summary from:", url);
    
    const response = await apiRequest(
      "POST", 
      url,
      childId ? { childId } : undefined
    );
    
    return await response.json();
  } catch (error) {
    console.error("Error getting document summary:", error);
    throw new Error("Failed to get document summary. Please try again.");
  }
}

// Function to analyze goals in an IEP document
export async function analyzeIEPGoals(documentId: string): Promise<any> {
  try {
    const response = await apiRequest(
      "GET", 
      `/api/ai/goals/${documentId}`
    );
    
    return await response.json();
  } catch (error) {
    console.error("Error analyzing IEP goals:", error);
    throw new Error("Failed to analyze IEP goals. Please try again.");
  }
}

// Function to provide suggested questions based on the document content
export async function getSuggestedQuestions(documentId: string): Promise<string[]> {
  try {
    const response = await apiRequest(
      "GET", 
      `/api/ai/suggested-questions/${documentId}`
    );
    
    return await response.json();
  } catch (error) {
    console.error("Error getting suggested questions:", error);
    throw new Error("Failed to get suggested questions. Please try again.");
  }
}
