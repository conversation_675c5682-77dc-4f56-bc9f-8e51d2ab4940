// Default prompt templates for interacting with OpenAI API

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  isDefault: boolean;
  isEditable: boolean;
}

export const defaultTemplates: PromptTemplate[] = [
  {
    id: "iep-analysis",
    name: "IEP Analysis",
    description: "General analysis of an IEP document",
    template: `Analyze this IEP document and provide a comprehensive summary that breaks down:
1. The child's current abilities and challenges
2. The specific goals set in the IEP
3. The accommodations and services being provided
4. Key timelines and progress monitoring methods
5. Areas that might need clarification or advocacy`,
    isDefault: true,
    isEditable: true
  },
  {
    id: "goal-breakdown",
    name: "Goal Breakdown",
    description: "Detailed explanation of IEP goals",
    template: `Extract and explain all the goals from this IEP document. For each goal:
1. Explain the goal in simple, non-technical language
2. Identify the baseline measurements
3. Describe how progress will be measured
4. Suggest home activities that could support this goal
5. Provide questions I could ask teachers about this goal`,
    isDefault: true,
    isEditable: true
  },
  {
    id: "service-analysis",
    name: "Service Analysis",
    description: "Breakdown of services and accommodations",
    template: `Analyze the services and accommodations in this IEP document:
1. List all services with frequency, duration, and provider
2. Explain all classroom and testing accommodations
3. Identify if any common accommodations are missing that might benefit my child
4. Explain how to track if services are being properly delivered
5. Provide questions I could ask about these services at my next meeting`,
    isDefault: true,
    isEditable: true
  },
  {
    id: "rights-explanation",
    name: "Parent Rights",
    description: "Explanation of parent rights related to the IEP",
    template: `Based on this IEP document, explain my rights as a parent:
1. What rights do I have to request changes or amendments?
2. What is the process for disagreeing with aspects of the IEP?
3. What timelines exist for reviews and updates?
4. What options do I have if I feel the IEP is not being followed?
5. When and how can I request reevaluations or additional assessments?`,
    isDefault: true,
    isEditable: true
  },
  {
    id: "meeting-prep",
    name: "Meeting Preparation",
    description: "Help prepare for an upcoming IEP meeting",
    template: `Help me prepare for my upcoming IEP meeting by reviewing this document:
1. Summarize the current IEP's key points
2. Identify areas where progress data might be insufficient
3. Suggest specific questions I should ask about each goal
4. Recommend additional services or accommodations I might want to discuss
5. Provide a checklist of things to bring and points to address`,
    isDefault: true,
    isEditable: true
  },
  {
    id: "progress-evaluation",
    name: "Progress Evaluation",
    description: "Evaluate progress based on reports",
    template: `Evaluate my child's progress based on this IEP and any progress reports:
1. For each goal, assess if progress is being made at an appropriate rate
2. Identify any goals where progress appears stalled or slow
3. Analyze if services are being delivered as specified
4. Suggest questions I should ask about areas with limited progress
5. Recommend potential adjustments to goals or services based on progress data`,
    isDefault: true,
    isEditable: true
  },
  {
    id: "custom",
    name: "Custom Prompt",
    description: "Your own custom prompt",
    template: "Enter your own specific instructions here for analyzing the IEP document...",
    isDefault: true,
    isEditable: true
  }
];

// Function to get a template by ID
export function getTemplateById(id: string): PromptTemplate {
  const template = defaultTemplates.find(t => t.id === id);
  if (!template) {
    return defaultTemplates[0]; // Return default if not found
  }
  return template;
}

// Local storage key for user-defined templates
const USER_TEMPLATES_KEY = 'iep-ai-user-templates';

// Function to save user's custom templates
export function saveUserTemplates(templates: PromptTemplate[]): void {
  localStorage.setItem(USER_TEMPLATES_KEY, JSON.stringify(templates));
}

// Function to load user's custom templates
export function loadUserTemplates(): PromptTemplate[] {
  const saved = localStorage.getItem(USER_TEMPLATES_KEY);
  if (!saved) return [];
  
  try {
    return JSON.parse(saved);
  } catch (e) {
    console.error('Failed to parse user templates', e);
    return [];
  }
}

// Function to get all templates (defaults + user custom)
export function getAllTemplates(): PromptTemplate[] {
  const userTemplates = loadUserTemplates();
  return [...defaultTemplates, ...userTemplates.filter(t => !t.isDefault)];
}

// Function to save a new template or update an existing one
export function saveTemplate(template: PromptTemplate): void {
  if (template.isDefault) {
    // If it's a default template, just update the user copy
    const userTemplates = loadUserTemplates();
    const index = userTemplates.findIndex(t => t.id === template.id);
    
    if (index >= 0) {
      userTemplates[index] = template;
    } else {
      userTemplates.push(template);
    }
    
    saveUserTemplates(userTemplates);
  } else {
    // If it's a user template, add/update it
    const userTemplates = loadUserTemplates();
    const index = userTemplates.findIndex(t => t.id === template.id);
    
    if (index >= 0) {
      userTemplates[index] = template;
    } else {
      // New template
      if (!template.id) {
        template.id = `user-${Date.now()}`;
      }
      userTemplates.push(template);
    }
    
    saveUserTemplates(userTemplates);
  }
}

// Function to delete a user template
export function deleteTemplate(id: string): void {
  const userTemplates = loadUserTemplates();
  const filtered = userTemplates.filter(t => t.id !== id);
  saveUserTemplates(filtered);
}