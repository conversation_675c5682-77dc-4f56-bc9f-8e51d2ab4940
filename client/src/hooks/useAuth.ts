
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useLocation } from "wouter";

export function useAuth() {
  const [, navigate] = useLocation();
  
  const { 
    data: user, 
    isLoading, 
    error, 
    isError,
    refetch
  } = useQuery({
    queryKey: ["/api/auth/user"],
    retry: 1, // Allow one retry
    refetchOnWindowFocus: false,
    refetchInterval: 300000, // Refresh every 5 minutes
    staleTime: 290000 // Consider data stale after ~5 minutes
    // Note: credentials option is already set in queryClient.ts for all requests
  });

  // Handle unauthorized user on protected pages
  useEffect(() => {
    if (isLoading) return;
    
    // Get current location
    const currentPath = window.location.pathname;
    
    // List of protected paths that require authentication
    const protectedPaths = [
      '/dashboard', 
      '/child-profile', 
      '/document-upload', 
      '/document-viewer', 
      '/conversation'
    ];
    
    // Check if current path is protected
    const isProtectedPath = protectedPaths.some(path => 
      currentPath === path || currentPath.startsWith(`${path}/`)
    );
    
    if (isError && isProtectedPath) {
      navigate('/');
      return;
    }
    
    // Check for stored redirect URL after authentication
    if (user && !isLoading) {
      const redirectUrl = sessionStorage.getItem('redirectAfterAuth');
      if (redirectUrl) {
        console.log("[DEBUG AUTH] Found stored redirect URL:", redirectUrl);
        sessionStorage.removeItem('redirectAfterAuth');
        // Extract just the path and query params from the stored URL
        const url = new URL(redirectUrl);
        const redirectPath = url.pathname + url.search;
        console.log("[DEBUG AUTH] Redirecting to:", redirectPath);
        navigate(redirectPath);
        return;
      }
      
      // Only redirect to dashboard from home if no stored redirect
      if (currentPath === '/') {
        navigate('/dashboard');
      }
    }
  }, [isLoading, user, isError, navigate]);

  return {
    user,
    isLoading,
    isAuthenticated: !!user && !isError,
    error,
    isError
  };
}
