@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 210, 66%, 43%;
  --primary-dark: 210, 66%, 38%;
  --primary-foreground: 211 100% 99%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 20 14.3% 4.1%;
  --radius: 0.5rem;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 210 66% 43%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
  
  svg {
    display: inline-block;
    vertical-align: middle;
  }
}

/* Typography Plugin Custom Styles */
@layer components {
  /* Custom prose styling that integrates with your design system */
  .prose {
    color: hsl(var(--foreground));
    max-width: none;
  }
  
  .prose h1 {
    color: hsl(var(--foreground));
    font-weight: 700;
    font-size: 1.125rem;
    line-height: 1.75rem;
    margin-bottom: 0.75rem;
    margin-top: 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid hsl(var(--border));
  }
  
  .prose h2 {
    color: hsl(var(--foreground));
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.5rem;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
  }
  
  .prose h3 {
    color: hsl(var(--foreground));
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.25rem;
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;
  }
  
  .prose p {
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
    margin-top: 0;
  }
  
  .prose ol {
    list-style-type: decimal;
    list-style-position: inside;
    margin-bottom: 1rem;
    padding-left: 0.5rem;
  }
  
  .prose ul {
    list-style-type: disc;
    list-style-position: inside;
    margin-bottom: 1rem;
    padding-left: 0.5rem;
  }
  
  .prose li {
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.25rem;
    padding-left: 0.5rem;
  }
  
  .prose li p {
    margin-bottom: 0.25rem;
    display: inline;
  }
  
  .prose strong {
    color: hsl(var(--foreground));
    font-weight: 600;
  }
  
  .prose code {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-family: ui-monospace, SFMono-Regular, "Segoe UI Mono", Consolas, "Liberation Mono", Menlo, monospace;
  }
  
  .prose pre {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
    padding: 0.75rem;
    border-radius: 0.375rem;
    overflow-x: auto;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
  
  .prose pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
  }
  
  .prose blockquote {
    border-left: 4px solid hsl(var(--primary));
    padding-left: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    background-color: hsl(var(--muted) / 0.5);
    font-style: italic;
    color: hsl(var(--muted-foreground));
  }
  
  .prose a {
    color: hsl(var(--primary));
    text-decoration: underline;
    text-underline-offset: 2px;
  }
  
  .prose a:hover {
    color: hsl(var(--primary-dark));
  }
  
  /* Prose invert for dark mode and user messages */
  .prose-invert h1,
  .prose-invert h2,
  .prose-invert h3,
  .prose-invert strong {
    color: white;
  }
  
  .prose-invert p,
  .prose-invert li {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .prose-invert code {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .prose-invert pre {
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
  }
  
  .prose-invert blockquote {
    border-left-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
  }
  
  .prose-invert a {
    color: rgba(255, 255, 255, 0.9);
  }
  
  /* Size variants */
  .prose-sm {
    font-size: 0.875rem;
    line-height: 1.5;
  }
  
  .prose-sm h1 {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  .prose-sm h2 {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .prose-sm h3 {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}