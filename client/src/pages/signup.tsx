import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import BetaBanner from "@/components/ui/beta-banner";

// Declare grecaptcha for TypeScript
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}

export default function SignupPage() {
  const [, navigate] = useLocation();
  
  // Form state
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  
  // reCAPTCHA state
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [recaptchaError, setRecaptchaError] = useState("");
  const [recaptchaLoaded, setRecaptchaLoaded] = useState(false);
  
  // Get site key from environment variable (Vite uses import.meta.env)
  const recaptchaSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY || "";

  const handleLoginClick = () => {
    navigate("/login");
  };

  const handleGoogleSignup = () => {
    // Check if we have a stored redirect URL from invitation flow
    const redirectUrl = sessionStorage.getItem('redirectAfterAuth');
    if (redirectUrl) {
      // Add the stored URL as a state parameter for Google OAuth
      const encodedUrl = encodeURIComponent(redirectUrl);
      window.location.href = `/api/auth/google?state=${encodedUrl}`;
    } else {
      window.location.href = "/api/auth/google";
    }
  };

  // Load reCAPTCHA v3 script
  useEffect(() => {
    if (!recaptchaSiteKey) {
      console.warn('VITE_RECAPTCHA_SITE_KEY not found in environment variables');
      return;
    }

    // Check if script is already loaded
    if (window.grecaptcha) {
      setRecaptchaLoaded(true);
      return;
    }

    // Load reCAPTCHA script
    const script = document.createElement('script');
    script.src = `https://www.google.com/recaptcha/api.js?render=${recaptchaSiteKey}`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      console.log('reCAPTCHA script loaded');
      if (window.grecaptcha) {
        window.grecaptcha.ready(() => {
          console.log('reCAPTCHA ready');
          setRecaptchaLoaded(true);
        });
      }
    };
    
    script.onerror = () => {
      console.error('Failed to load reCAPTCHA script');
      setRecaptchaError("Failed to load security verification");
    };

    document.head.appendChild(script);

    // Cleanup function
    return () => {
      // Note: We don't remove the script as it might be used by other components
    };
  }, [recaptchaSiteKey]);

  // Execute reCAPTCHA v3
  const executeRecaptcha = async (): Promise<string | null> => {
    if (!window.grecaptcha || !recaptchaLoaded) {
      setRecaptchaError("Security verification not ready. Please try again.");
      return null;
    }

    try {
      console.log('Executing reCAPTCHA...');
      const token = await window.grecaptcha.execute(recaptchaSiteKey, { 
        action: 'signup' 
      });
      console.log('reCAPTCHA token generated');
      setRecaptchaError("");
      return token;
    } catch (error) {
      console.error('reCAPTCHA execution failed:', error);
      setRecaptchaError("Security verification failed. Please try again.");
      return null;
    }
  };

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setPasswordErrors([]);
    setRecaptchaError("");

    // Client-side validation
    if (!acceptTerms) {
      setError("You must agree to the Terms of Service and Privacy Policy");
      setIsLoading(false);
      return;
    }

    // Execute reCAPTCHA v3
    const token = await executeRecaptcha();
    if (!token) {
      setIsLoading(false);
      return; // Error message already set by executeRecaptcha
    }

    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          email,
          password,
          firstName,
          lastName,
          recaptchaToken: token,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Registration successful - check for stored redirect URL
        const redirectUrl = sessionStorage.getItem('redirectAfterAuth');
        if (redirectUrl) {
          console.log("[DEBUG SIGNUP] Found stored redirect URL:", redirectUrl);
          sessionStorage.removeItem('redirectAfterAuth');
          // Extract just the path and query params from the stored URL
          const url = new URL(redirectUrl);
          const redirectPath = url.pathname + url.search;
          console.log("[DEBUG SIGNUP] Redirecting to:", redirectPath);
          window.location.href = redirectPath;
        } else {
          navigate("/dashboard");
          // Optionally refresh the page to update auth state
          window.location.reload();
        }
      } else {
        // Registration failed
        if (data.code === 'RECAPTCHA_VERIFICATION_FAILED') {
          setRecaptchaError(data.message || "Security verification failed");
        } else if (data.errors) {
          setPasswordErrors(data.errors);
        } else {
          setError(data.message || "Registration failed");
        }
      }
    } catch (error) {
      console.error("Registration error:", error);
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <BetaBanner />
      <Navbar />
      <main className="min-h-screen flex items-center justify-center bg-neutral-50 py-12">
        <div className="bg-white rounded-lg max-w-md w-full p-8 shadow-lg max-h-[90vh] overflow-y-auto">
          <h2 className="text-2xl font-bold text-neutral-900 mb-6 text-center">Join IEPs.ai</h2>
          
          <div className="space-y-4">
            <Button 
              variant="outline" 
              className="w-full flex items-center justify-center"
              onClick={handleGoogleSignup}
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.24 10.285V14.4h6.806c-.275 1.765-2.056 5.174-6.806 5.174-4.095 0-7.439-3.389-7.439-7.574s3.345-7.574 7.439-7.574c2.33 0 3.891.989 4.785 1.849l3.254-3.138C18.189 1.186 15.479 0 12.24 0c-6.635 0-12 5.365-12 12s5.365 12 12 12c6.926 0 11.52-4.869 11.52-11.726 0-.788-.085-1.39-.189-1.989H12.24z" fill="#4285F4"/>
              </svg>
              Continue with Google
            </Button>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-neutral-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-neutral-500">Or continue with email</span>
              </div>
            </div>
            
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}
            
            {passwordErrors.length > 0 && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                <p className="font-medium mb-1">Password requirements not met:</p>
                <ul className="list-disc list-inside space-y-1">
                  {passwordErrors.map((err, index) => (
                    <li key={index}>{err}</li>
                  ))}
                </ul>
              </div>
            )}

            {recaptchaError && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {recaptchaError}
              </div>
            )}
            
            <form onSubmit={handleEmailSignup} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first-name">First Name</Label>
                  <Input 
                    id="first-name" 
                    type="text" 
                    placeholder="John" 
                    className="mt-1"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Label htmlFor="last-name">Last Name</Label>
                  <Input 
                    id="last-name" 
                    type="text" 
                    placeholder="Doe" 
                    className="mt-1"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="email">Email address</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  className="mt-1"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </div>
              
              <div>
                <Label htmlFor="password">Password</Label>
                <Input 
                  id="password" 
                  type="password" 
                  placeholder="Create a secure password" 
                  className="mt-1"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                />
                <p className="text-xs text-neutral-500 mt-1">
                  Must be at least 8 characters with uppercase, lowercase, and number
                </p>
              </div>
              
              <div className="flex items-start space-x-2">
                <Checkbox 
                  id="terms" 
                  checked={acceptTerms}
                  onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
                  disabled={isLoading}
                />
                <Label htmlFor="terms" className="text-sm text-neutral-600">
                  I agree to the <a href="#" className="text-primary hover:text-primary-dark">Terms of Service</a> and <a href="#" className="text-primary hover:text-primary-dark">Privacy Policy</a>
                </Label>
              </div>

              {/* Show warning if reCAPTCHA site key is not configured */}
              {!recaptchaSiteKey && (
                <div className="p-3 text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md">
                  reCAPTCHA not configured. Please set VITE_RECAPTCHA_SITE_KEY environment variable.
                </div>
              )}

              {/* Show reCAPTCHA status */}
              {recaptchaSiteKey && (
                <div className="text-sm text-neutral-500 text-center">
                  <span className={recaptchaLoaded ? "text-green-600" : "text-amber-600"}>
                    {recaptchaLoaded ? "✓ Security verification ready" : "⏳ Loading security verification..."}
                  </span>
                  <div className="flex items-center justify-center mt-1">
                    <img 
                      src="https://www.google.com/recaptcha/api2/anchor?ar=1&k=test&co=aHR0cHM6Ly93d3cuZ29vZ2xlLmNvbTo0NDM.&hl=en&v=1qjz1sEPuVJq6qABjL3hDIjK&size=invisible&cb=123" 
                      alt="reCAPTCHA" 
                      className="w-4 h-4 opacity-30"
                      style={{visibility: 'hidden', position: 'absolute'}}
                    />
                    <span className="text-xs text-neutral-400">
                      Protected by reCAPTCHA v3
                    </span>
                  </div>
                </div>
              )}
              
              <Button 
                type="submit" 
                className="w-full bg-primary hover:bg-primary-dark"
                disabled={isLoading || !acceptTerms || (recaptchaSiteKey && !recaptchaLoaded)}
              >
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>
            </form>
            
            <p className="text-center text-sm text-neutral-600">
              Already have an account? <Button variant="link" className="text-primary hover:text-primary-dark p-0" onClick={handleLoginClick}>Log in</Button>
            </p>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}