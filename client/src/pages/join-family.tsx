import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, CheckCircle, XCircle, Loader2 } from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";

export default function JoinFamily() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading } = useAuth();
  const [invitationId, setInvitationId] = useState<string | null>(null);

  // Extract invitation ID from URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const invitationParam = urlParams.get('invitation');
    console.log("[DEBUG FRONTEND] URL params:", window.location.search);
    console.log("[DEBUG FRONTEND] Extracted invitation ID:", invitationParam);
    if (invitationParam) {
      setInvitationId(invitationParam);
    }
  }, []);

  // Query for invitation details
  const { data: invitation, isLoading: isLoadingInvitation, error: invitationError } = useQuery({
    queryKey: [`/api/family-invitations/${invitationId}`],
    enabled: !!invitationId,
    retry: false
  });

  // Auto-accept invitation if user just returned from authentication
  useEffect(() => {
    const justAuthenticated = sessionStorage.getItem('justAuthenticated');
    if (justAuthenticated && isAuthenticated && invitation && invitation.status === 'pending' && user?.email === invitation.email) {
      console.log("[DEBUG] Auto-accepting invitation after authentication");
      sessionStorage.removeItem('justAuthenticated');
      acceptInvitationMutation.mutate();
    }
  }, [invitation, isLoadingInvitation, invitationError, isAuthenticated, user]);

  // Mutation to accept invitation
  const acceptInvitationMutation = useMutation({
    mutationFn: async () => {
      console.log("[DEBUG FRONTEND] Accept invitation clicked, invitationId:", invitationId);
      if (!invitationId) throw new Error("No invitation ID");
      console.log("[DEBUG FRONTEND] Making API request to:", `/api/family-invitations/${invitationId}/accept`);
      const response = await apiRequest('POST', `/api/family-invitations/${invitationId}/accept`, {});
      console.log("[DEBUG FRONTEND] API response:", response);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Invitation accepted!",
        description: "You now have access to the family workspace.",
      });
      navigate('/dashboard');
    },
    onError: (error) => {
      toast({
        title: "Failed to accept invitation",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive"
      });
    }
  });

  // Mutation to decline invitation
  const declineInvitationMutation = useMutation({
    mutationFn: async () => {
      if (!invitationId) throw new Error("No invitation ID");
      const response = await apiRequest('POST', `/api/family-invitations/${invitationId}/decline`, {});
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Invitation declined",
        description: "You have declined the family invitation.",
      });
      navigate('/');
    },
    onError: (error) => {
      toast({
        title: "Failed to decline invitation",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive"
      });
    }
  });

  // Show loading state
  if (isLoading || isLoadingInvitation) {
    return (
      <>
        <Navbar />
        <main className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading invitation...</p>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  // Handle missing invitation ID
  if (!invitationId) {
    return (
      <>
        <Navbar />
        <main className="max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <XCircle className="h-6 w-6 mr-2 text-red-500" />
                Invalid Invitation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 mb-4">
                The invitation link is invalid or incomplete. Please check your email for the correct link.
              </p>
              <Button onClick={() => navigate('/')} variant="outline">
                Go Home
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </>
    );
  }

  // Handle invitation not found or error
  if (invitationError || !invitation) {
    return (
      <>
        <Navbar />
        <main className="max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <XCircle className="h-6 w-6 mr-2 text-red-500" />
                Invitation Not Found
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 mb-4">
                This invitation may have expired, been revoked, or already been used. Please contact the person who sent you the invitation.
              </p>
              <Button onClick={() => navigate('/')} variant="outline">
                Go Home
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </>
    );
  }

  // Handle invitation already processed
  if (invitation.status !== 'pending') {
    const isAccepted = invitation.status === 'accepted';
    return (
      <>
        <Navbar />
        <main className="max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {isAccepted ? (
                  <CheckCircle className="h-6 w-6 mr-2 text-green-500" />
                ) : (
                  <XCircle className="h-6 w-6 mr-2 text-red-500" />
                )}
                Invitation {isAccepted ? 'Accepted' : 'Declined'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 mb-4">
                This invitation has already been {invitation.status}.
              </p>
              <Button onClick={() => navigate(isAccepted ? '/dashboard' : '/')} variant="outline">
                {isAccepted ? 'Go to Dashboard' : 'Go Home'}
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <>
        <Navbar />
        <main className="max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-6 w-6 mr-2 text-blue-500" />
                Family Workspace Invitation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 mb-4">
                <strong>{invitation.inviterName}</strong> has invited you to join their family workspace on IEPs.ai.
              </p>
              <p className="text-neutral-600 mb-6">
                You need to sign in or create an account to accept this invitation.
              </p>
              <div className="flex space-x-4">
                <Button onClick={() => {
                  console.log("[DEBUG FRONTEND] Sign In button clicked");
                  // Store the current invitation URL to redirect back after auth
                  sessionStorage.setItem('redirectAfterAuth', window.location.href);
                  sessionStorage.setItem('justAuthenticated', 'true');
                  console.log("[DEBUG FRONTEND] Stored redirect URL:", window.location.href);
                  console.log("[DEBUG FRONTEND] Navigating to /login");
                  navigate('/login');
                }} className="bg-primary hover:bg-primary-dark">
                  Sign In
                </Button>
                <Button onClick={() => {
                  console.log("[DEBUG FRONTEND] Create Account button clicked");
                  // Store the current invitation URL to redirect back after auth
                  sessionStorage.setItem('redirectAfterAuth', window.location.href);
                  sessionStorage.setItem('justAuthenticated', 'true');
                  console.log("[DEBUG FRONTEND] Stored redirect URL:", window.location.href);
                  console.log("[DEBUG FRONTEND] Navigating to /signup");
                  navigate('/signup');
                }} variant="outline">
                  Create Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </>
    );
  }

  // Check if invitation is for current user's email
  if (invitation.email !== user?.email) {
    return (
      <>
        <Navbar />
        <main className="max-w-4xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <XCircle className="h-6 w-6 mr-2 text-red-500" />
                Wrong Account
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 mb-4">
                This invitation was sent to <strong>{invitation.email}</strong>, but you're signed in as <strong>{user?.email}</strong>.
              </p>
              <p className="text-neutral-600 mb-6">
                Please sign in with the correct account or ask for a new invitation.
              </p>
              <div className="flex space-x-4">
                <Button onClick={() => navigate('/login')} variant="outline">
                  Sign In with Different Account
                </Button>
                <Button onClick={() => navigate('/')} variant="outline">
                  Go Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </>
    );
  }

  // Main invitation acceptance UI
  return (
    <>
      <Navbar />
      <main className="max-w-4xl mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-6 w-6 mr-2 text-blue-500" />
              Family Workspace Invitation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <p className="text-neutral-600 mb-2">
                  <strong>{invitation.inviterName}</strong> has invited you to join their family workspace on IEPs.ai.
                </p>
                <p className="text-neutral-600">
                  By accepting this invitation, you'll be able to:
                </p>
                <ul className="mt-2 ml-4 space-y-1 text-neutral-600">
                  <li>• View and manage IEP documents</li>
                  <li>• Track progress together</li>
                  <li>• Get AI-powered insights</li>
                  <li>• Collaborate on your child's education journey</li>
                </ul>
              </div>

              <div className="flex space-x-4">
                <Button 
                  onClick={() => acceptInvitationMutation.mutate()}
                  disabled={acceptInvitationMutation.isPending || declineInvitationMutation.isPending}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {acceptInvitationMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Accepting...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Accept Invitation
                    </>
                  )}
                </Button>
                <Button 
                  onClick={() => declineInvitationMutation.mutate()}
                  disabled={acceptInvitationMutation.isPending || declineInvitationMutation.isPending}
                  variant="outline"
                >
                  {declineInvitationMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Declining...
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Decline
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </>
  );
}