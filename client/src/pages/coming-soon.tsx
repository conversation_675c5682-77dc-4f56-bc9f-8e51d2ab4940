import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import { Link } from "wouter";

export default function ComingSoon() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-yellow-500" />
            <h1 className="text-2xl font-bold text-gray-900">Coming Soon...</h1>
          </div>

          <p className="mt-4 text-sm text-gray-600">
            IEPs.ai is currently in beta, and this feature is not yet available.
          </p>
          
          <p className="mt-2 text-sm text-gray-500">
            You can check our <Link href="/roadmap">product roadmap</Link> for updates on when this and other features will be released.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}