import { useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import BetaBanner from "@/components/ui/beta-banner";

export default function LoginPage() {
  const [, navigate] = useLocation();
  
  // Form state
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  
  // Forgot password state
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState("");
  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);
  const [forgotPasswordMessage, setForgotPasswordMessage] = useState("");

  const handleGoogleLogin = () => {
    // Check if we have a stored redirect URL from invitation flow
    const redirectUrl = sessionStorage.getItem('redirectAfterAuth');
    if (redirectUrl) {
      // Add the stored URL as a state parameter for Google OAuth
      const encodedUrl = encodeURIComponent(redirectUrl);
      window.location.href = `/api/auth/google?state=${encodedUrl}`;
    } else {
      window.location.href = "/api/auth/google";
    }
  };

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          email,
          password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Login successful - check for stored redirect URL
        const redirectUrl = sessionStorage.getItem('redirectAfterAuth');
        if (redirectUrl) {
          console.log("[DEBUG LOGIN] Found stored redirect URL:", redirectUrl);
          sessionStorage.removeItem('redirectAfterAuth');
          // Extract just the path and query params from the stored URL
          const url = new URL(redirectUrl);
          const redirectPath = url.pathname + url.search;
          console.log("[DEBUG LOGIN] Redirecting to:", redirectPath);
          window.location.href = redirectPath;
        } else {
          navigate("/dashboard");
          // Optionally refresh the page to update auth state
          window.location.reload();
        }
      } else {
        // Login failed
        setError(data.message || "Login failed");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setForgotPasswordLoading(true);
    setError("");
    setForgotPasswordMessage("");

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: forgotPasswordEmail,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setForgotPasswordMessage(data.message);
      } else {
        setError(data.message || "Failed to send reset email");
      }
    } catch (error) {
      console.error("Forgot password error:", error);
      setError("Network error. Please try again.");
    } finally {
      setForgotPasswordLoading(false);
    }
  };

  const resetToLogin = () => {
    setShowForgotPassword(false);
    setForgotPasswordEmail("");
    setForgotPasswordMessage("");
    setError("");
  };

  const handleSignupClick = () => {
    navigate("/signup");
  };

  return (
    <>
      <BetaBanner />
      <Navbar />
      <main className="min-h-screen flex items-center justify-center bg-neutral-50 py-12">
        <div className="bg-white rounded-lg max-w-md w-full p-8 shadow-lg">
          {!showForgotPassword ? (
            <>
              <h2 className="text-2xl font-bold text-neutral-900 mb-6 text-center">Welcome Back</h2>
              
              <div className="space-y-4">
                <Button 
                  variant="outline" 
                  className="w-full flex items-center justify-center"
                  onClick={handleGoogleLogin}
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.24 10.285V14.4h6.806c-.275 1.765-2.056 5.174-6.806 5.174-4.095 0-7.439-3.389-7.439-7.574s3.345-7.574 7.439-7.574c2.33 0 3.891.989 4.785 1.849l3.254-3.138C18.189 1.186 15.479 0 12.24 0c-6.635 0-12 5.365-12 12s5.365 12 12 12c6.926 0 11.52-4.869 11.52-11.726 0-.788-.085-1.39-.189-1.989H12.24z" fill="#4285F4"/>
                  </svg>
                  Continue with Google
                </Button>
                
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-neutral-300"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-neutral-500">Or continue with email</span>
                  </div>
                </div>
                
                {error && (
                  <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                    {error}
                  </div>
                )}
                
                <form onSubmit={handleEmailLogin} className="space-y-4">
                  <div>
                    <Label htmlFor="login-email">Email address</Label>
                    <Input 
                      id="login-email" 
                      type="email" 
                      placeholder="<EMAIL>" 
                      className="mt-1"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="login-password">Password</Label>
                    <Input 
                      id="login-password" 
                      type="password" 
                      placeholder="Enter your password" 
                      className="mt-1"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="remember-me" 
                        checked={rememberMe}
                        onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                        disabled={isLoading}
                      />
                      <Label htmlFor="remember-me" className="text-sm text-neutral-600">Remember me</Label>
                    </div>
                    <button 
                      type="button"
                      className="text-sm text-primary hover:text-primary-dark"
                      onClick={() => setShowForgotPassword(true)}
                    >
                      Forgot password?
                    </button>
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full bg-primary hover:bg-primary-dark"
                    disabled={isLoading}
                  >
                    {isLoading ? "Logging in..." : "Log In"}
                  </Button>
                </form>
                
                <p className="text-center text-sm text-neutral-600">
                  Don't have an account? <Button variant="link" className="text-primary hover:text-primary-dark p-0" onClick={handleSignupClick}>Sign up</Button>
                </p>
              </div>
            </>
          ) : (
            <>
              <h2 className="text-2xl font-bold text-neutral-900 mb-6 text-center">Reset Password</h2>
              
              <div className="space-y-4">
                {forgotPasswordMessage ? (
                  <>
                    <div className="p-4 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
                      {forgotPasswordMessage}
                    </div>
                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={resetToLogin}
                    >
                      Back to Login
                    </Button>
                  </>
                ) : (
                  <>
                    <p className="text-sm text-neutral-600 text-center mb-4">
                      Enter your email address and we'll send you a link to reset your password.
                    </p>
                    
                    {error && (
                      <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    <form onSubmit={handleForgotPassword} className="space-y-4">
                      <div>
                        <Label htmlFor="forgot-email">Email address</Label>
                        <Input 
                          id="forgot-email" 
                          type="email" 
                          placeholder="<EMAIL>" 
                          className="mt-1"
                          value={forgotPasswordEmail}
                          onChange={(e) => setForgotPasswordEmail(e.target.value)}
                          required
                          disabled={forgotPasswordLoading}
                        />
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="w-full bg-primary hover:bg-primary-dark"
                        disabled={forgotPasswordLoading}
                      >
                        {forgotPasswordLoading ? "Sending..." : "Send Reset Link"}
                      </Button>
                    </form>
                    
                    <Button 
                      variant="link" 
                      className="w-full text-neutral-600 hover:text-neutral-800"
                      onClick={resetToLogin}
                    >
                      Back to Login
                    </Button>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </main>
      <Footer />
    </>
  );
}