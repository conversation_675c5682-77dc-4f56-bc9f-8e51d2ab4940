import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Link, useLocation, useParams } from "wouter";
import { queryClient } from "@/lib/queryClient";
import { ArrowLeft, Upload, ChevronRight } from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function DocumentUpload() {
  const { childId } = useParams();
  const { isAuthenticated, isLoading } = useAuth();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [documentTitle, setDocumentTitle] = useState("");
  const [documentType, setDocumentType] = useState("IEP");
  const [documentSchoolYear, setDocumentSchoolYear] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fileName, setFileName] = useState("");
  
  // Redirect to home if not authenticated
  if (!isLoading && !isAuthenticated) {
    navigate('/');
    return null;
  }
  
  // Query for child information
  const { data: child, isLoading: isLoadingChild } = useQuery({
    queryKey: [`/api/children/${childId}`],
    enabled: isAuthenticated && !!childId,
  });
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      console.log("File selected:", file.name, "Size:", file.size, "Type:", file.type);
      
      // Check file size - max 50MB
      if (file.size > 50 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please upload files smaller than 50MB. For larger files, please split or compress them first.",
          variant: "destructive"
        });
        e.target.value = ''; // Reset file input
        return;
      }
      
      setDocumentFile(file);
      setFileName(file.name);
      
      // Auto-fill title if empty
      if (!documentTitle) {
        const fileNameWithoutExt = file.name.split('.')[0];
        setDocumentTitle(fileNameWithoutExt);
      }
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!childId) {
      toast({
        title: "Error",
        description: "Missing child information. Please try again.",
        variant: "destructive"
      });
      return;
    }
    
    if (!documentFile) {
      toast({
        title: "Missing document",
        description: "Please select a file to upload.",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Super simple - just upload the file with minimal metadata
      const formData = new FormData();
      formData.append('document', documentFile);
      
      console.log("Starting upload for child ID:", childId);
      console.log("File name:", documentFile.name);
      console.log("File size:", documentFile.size);
      
      toast({
        title: "Uploading document...",
        description: "Please wait while your document is being uploaded."
      });
      
      // Simple fetch with text response
      const response = await fetch(`/api/children/${childId}/documents/upload`, {
        method: 'POST',
        body: formData
      });
      
      console.log("Upload response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || "Failed to upload document");
      }
      
      const resultText = await response.text();
      console.log("Upload result:", resultText);
      
      // Show success message
      toast({
        title: "Document uploaded",
        description: "Your document has been uploaded successfully."
      });
      
      // Refresh data and return to dashboard
      queryClient.invalidateQueries({ queryKey: ['/api/children'] });
      navigate('/dashboard');
      
    } catch (error) {
      console.error("Upload error:", error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "There was a problem uploading your document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <>
      <Navbar />
      <main className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <div className="flex items-center text-sm text-neutral-600 mb-2">
            <Link href="/dashboard" className="text-primary hover:text-primary-dark">
              Dashboard
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link href={`/child-profile/${childId}`} className="text-primary hover:text-primary-dark">
              {!isLoadingChild && child ? `${child.name}'s Profile` : 'Child Profile'}
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-neutral-800">Upload Document</span>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">
              Upload IEP Document
              {!isLoadingChild && child && (
                <span className="text-primary"> for {child.name}</span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="documentTitle">Document Title</Label>
                <Input 
                  id="documentTitle" 
                  value={documentTitle} 
                  onChange={(e) => setDocumentTitle(e.target.value)} 
                  placeholder="E.g., Spring 2024 IEP"
                  className="mt-1"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="documentType">Document Type</Label>
                <select 
                  id="documentType"
                  value={documentType}
                  onChange={(e) => setDocumentType(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                  required
                >
                  <option value="IEP">IEP</option>
                  <option value="Evaluation">Evaluation</option>
                  <option value="Progress Report">Progress Report</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div>
                <Label htmlFor="documentSchoolYear">School Year</Label>
                <Input 
                  id="documentSchoolYear" 
                  value={documentSchoolYear} 
                  onChange={(e) => setDocumentSchoolYear(e.target.value)} 
                  placeholder="E.g., 2023-2024"
                  className="mt-1"
                />
                <p className="text-xs text-neutral-500 mt-1">
                  Optional - the current year will be used if left blank
                </p>
              </div>
              
              <div>
                <Label htmlFor="documentFile">Document File</Label>
                <div className="mt-1">
                  <Input
                    id="documentFile"
                    type="file"
                    accept=".pdf,.doc,.docx,.txt"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <div className="flex flex-col gap-2">
                    <div className="relative border border-dashed border-neutral-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-neutral-50" onClick={() => document.getElementById('documentFile')?.click()}>
                      <Upload className="h-8 w-8 text-neutral-400 mb-2" />
                      <p className="text-sm text-center font-medium">
                        {fileName ? fileName : "Click to upload a document"}
                      </p>
                      <p className="text-xs text-neutral-500 text-center mt-1">
                        Upload an IEP document, evaluation, or related file (PDF, Word, or Text)
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3 justify-end pt-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => navigate('/dashboard')}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={isSubmitting || !documentFile}
                >
                  {isSubmitting ? "Uploading..." : "Upload Document"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </>
  );
}