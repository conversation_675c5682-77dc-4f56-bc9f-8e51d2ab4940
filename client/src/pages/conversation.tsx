import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link, useParams, useLocation } from "wouter";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useStreamingMessage } from "@/hooks/useStreamingMessage";
import { 
  ArrowLeft, MessageSquare, Send, Loader2, FileText,
  Plus, RefreshCw, Pencil, AlertTriangle, Copy, Check, ChevronRight
} from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import FeedbackWidget from "@/components/FeedbackWidget";
import ReactMarkdown from 'react-markdown';

export default function ConversationPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const params = useParams();
  const [location, navigate] = useLocation();
  const { toast } = useToast();

  // Copy functionality state
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);

  // Streaming hook
  const {
    streamingMessage,
    isStreaming,
    error: streamingError,
    sendStreamingMessage,
    clearStreamingMessage,
  } = useStreamingMessage();

  // Extract parameters from URL
  const childId = params.childId;
  const searchParams = new URLSearchParams(window.location.search);
  const conversationId = searchParams.get('conversation');

  console.log("Page parameters:", { childId, conversationId });

  // State management
  const [message, setMessage] = useState("");
  const hasTriedAutoGenerate = useRef(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Upload modal state
  const [showUploadDocumentModal, setShowUploadDocumentModal] = useState(false);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [documentTitle, setDocumentTitle] = useState("");
  const [documentType, setDocumentType] = useState("IEP");
  const [documentSchoolYear, setDocumentSchoolYear] = useState("");

  // Query for child information
  const { data: child, isLoading: isChildLoading } = useQuery({
    queryKey: [`/api/children/${childId}`],
    enabled: isAuthenticated && !!childId,
  });

  // Query for child's documents
  const { data: documents, isLoading: isLoadingDocuments } = useQuery({
    queryKey: [`/api/children/${childId}/documents`],
    enabled: isAuthenticated && !!childId,
  });

  // Query for child's conversations
  const { data: conversations, isLoading: isLoadingConversations } = useQuery({
    queryKey: [`/api/children/${childId}/conversations`],
    enabled: isAuthenticated && !!childId,
  });

  // Get the first conversation or create one
  const conversation = conversations && conversations.length > 0 ? conversations[0] : null;

  // Fetch messages for the conversation
  const { data: messages, isLoading: isMessagesLoading } = useQuery({
    queryKey: ['/api/conversations/' + (conversation?.id || '') + '/messages'],
    enabled: !!conversation?.id && isAuthenticated,
    onSuccess: (data) => {
      console.log("Messages loaded:", data);
      // Debug timestamp formats
      if (data && Array.isArray(data)) {
        data.forEach((msg: any, index: number) => {
          console.log(`Message ${index} timestamp:`, {
            createdAt: msg.createdAt,
            timestamp: msg.timestamp,
            created_at: msg.created_at,
            typeof_createdAt: typeof msg.createdAt,
            parsed: new Date(msg.createdAt || msg.timestamp || msg.created_at)
          });
        });
      }
    }
  });

  // Copy message function
  const copyMessage = async (messageId: string, content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      
      // Optional: Call backend to log the copy event
      if (conversation?.id) {
        try {
          await apiRequest('POST', `/api/conversations/${conversation.id}/messages/${messageId}/copy`, {});
        } catch (error) {
          console.log("Note: Copy tracking failed (this won't affect functionality):", error);
        }
      }
      
      toast({
        title: "Copied!",
        description: "Message copied to clipboard",
      });
      
      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopiedMessageId(null);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      // Fallback for older browsers
      fallbackCopyTextToClipboard(content, messageId);
    }
  };

  // Fallback copy method for older browsers
  const fallbackCopyTextToClipboard = (text: string, messageId: string) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
      setCopiedMessageId(messageId);
      toast({
        title: "Copied!",
        description: "Message copied to clipboard",
      });
      setTimeout(() => setCopiedMessageId(null), 2000);
    } catch (err) {
      console.error('Fallback: Could not copy text: ', err);
      toast({
        title: "Copy failed",
        description: "Unable to copy message to clipboard",
        variant: "destructive"
      });
    }

    document.body.removeChild(textArea);
  };

  // Check if summary is outdated (new documents added since last summary)
  const isSummaryOutdated = conversation && documents && 
    conversation.hasSummary && 
    documents.length > (conversation.documentCountAtSummary || 0);

  // Generate initial summary mutation
  const generateSummaryMutation = useMutation({
    mutationFn: async () => {
      console.log("Generating summary for child:", childId);
      const response = await apiRequest(
        'POST',
        `/api/ai/summary/child/${childId}`,
        { childId }
      );
      
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        const textContent = await response.text();
        console.error("Non-JSON response received:", textContent.substring(0, 200));
        throw new Error("Invalid response format from summary API");
      }
    },
    onSuccess: async (data) => {
      console.log("Summary generated successfully");
      try {
        let targetConversationId = conversation?.id;
        
        // Create conversation if it doesn't exist
        if (!targetConversationId) {
          console.log("Creating new conversation");
          const conversationResponse = await apiRequest(
            'POST',
            `/api/children/${childId}/conversations`,
            { 
              title: `Analysis of ${child?.name}'s IEP Documents`,
              hasSummary: true,  // Set the flag when creating
              documentCountAtSummary: documents?.length || 0  // Track document count
            }
          );
          const newConversation = await conversationResponse.json();
          targetConversationId = newConversation.id;
        } else {
          // Update existing conversation to mark summary as generated
          await apiRequest(
            'PATCH',
            `/api/conversations/${targetConversationId}`,
            { 
              hasSummary: true,
              documentCountAtSummary: documents?.length || 0
            }
          );
        }
        
        // Add the summary as the first message
        await apiRequest(
          'POST',
          `/api/conversations/${targetConversationId}/messages/direct`,
          {
            content: `# Document Summary\n\n${data.answer}\n\nI've analyzed ${child?.name || 'your child'}'s educational documents. What questions do you have?`,
            role: 'assistant',
            timestamp: new Date().toISOString()
          }
        );
        
        // Refresh conversations list and messages
        queryClient.invalidateQueries({ 
          queryKey: [`/api/children/${childId}/conversations`] 
        });
        queryClient.invalidateQueries({ 
          queryKey: [`/api/conversations/${targetConversationId}/messages`] 
        });
        
        toast({
          title: "Summary Generated",
          description: "Document summary has been created successfully.",
        });
      } catch (error) {
        console.error("Error creating conversation or message:", error);
        throw error;
      }
    },
    onError: (error) => {
      console.error("Error generating summary:", error);
      toast({
        title: "Error generating summary",
        description: "There was a problem creating the document summary. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest(
        'POST',
        `/api/children/${childId}/conversations`,
        { title: `Conversation about ${child?.name || 'your child'}'s IEP` }
      );
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: [`/api/children/${childId}/conversations`] 
      });
      toast({
        title: "Conversation created",
        description: "Your conversation has been created successfully."
      });
    }
  });

  // Send message mutation with streaming
  const sendMessageMutation = useMutation({
    mutationFn: async ({ content }: { content: string }) => {
      // Create conversation if it doesn't exist
      let targetConversationId = conversation?.id;
      if (!targetConversationId) {
        const newConversation = await createConversationMutation.mutateAsync();
        targetConversationId = newConversation.id;
      }

      // Use streaming instead of regular API call
      await sendStreamingMessage(targetConversationId, content, () => {
        // Invalidate messages query when streaming completes
        if (targetConversationId) {
          queryClient.invalidateQueries({ 
            queryKey: [`/api/conversations/${targetConversationId}/messages`] 
          });
        }
        console.log("Message sent successfully via streaming");
      });
      
      // Return success (the actual response handling is done by the streaming hook)
      return { success: true };
    },
    onError: (error) => {
      console.error("Error sending message:", error);
      toast({
        title: "Error sending message",
        description: "There was a problem sending your message. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Upload document mutation
  const uploadDocumentMutation = useMutation({
    mutationFn: async () => {
      if (!childId || !documentFile) return null;
      
      const formData = new FormData();
      formData.append('document', documentFile);
      formData.append('title', documentTitle || documentFile.name);
      formData.append('documentType', documentType);
      formData.append('schoolYear', documentSchoolYear || '');
      
      const response = await fetch(`/api/children/${childId}/documents/upload`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to upload document");
      }
      
      return await response.json();
    },
    onSuccess: (data) => {
      // Reset form fields
      setDocumentFile(null);
      setDocumentTitle("");
      setDocumentType("IEP");
      setDocumentSchoolYear("");
      setShowUploadDocumentModal(false);
      
      // Refresh documents data
      queryClient.invalidateQueries({ queryKey: [`/api/children/${childId}/documents`] });
      
      toast({
        title: "Document uploaded",
        description: "Your document has been uploaded successfully."
      });
    },
    onError: (error) => {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "There was a problem uploading your document. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Show streaming errors in toast
  useEffect(() => {
    if (streamingError) {
      toast({
        title: "Streaming Error",
        description: streamingError,
        variant: "destructive"
      });
    }
  }, [streamingError, toast]);

  // Updated auto-generation logic with database tracking
  useEffect(() => {
    console.log("🔍 useEffect triggered with dependencies:", {
      isAuthenticated,
      childId,
      documentsLength: documents?.length,
      isLoadingConversations,
      hasTriedAutoGenerate: hasTriedAutoGenerate.current,
      hasConversation: !!conversation,
      conversationHasSummary: conversation?.hasSummary,
      conversationSummary: !!conversation?.summary,
      messagesLength: messages?.length,
      timestamp: new Date().toISOString()
    });

    // Check if we already have a stored summary that matches the current document count
    const hasValidStoredSummary = (
      conversation?.hasSummary && 
      conversation?.summary && 
      conversation?.documentCountAtSummary === documents?.length
    );

    // Only auto-generate if:
    // 1. User is authenticated and we have a childId
    // 2. Documents exist
    // 3. Not currently loading conversations
    // 4. Haven't tried auto-generation yet in this session
    // 5. Either no conversation exists OR conversation exists but doesn't have a valid summary yet
    // 6. No messages exist (to avoid duplicate summaries)
    const shouldAutoGenerate = (
      isAuthenticated &&
      childId &&
      documents &&
      documents.length > 0 &&
      !isLoadingConversations &&
      !hasTriedAutoGenerate.current &&
      !hasValidStoredSummary &&
      (!messages || messages.length === 0)
    );

    if (shouldAutoGenerate) {
      console.log("🚀 Auto-generating summary for child:", childId, "documents:", documents.length, "at:", new Date().toISOString());
      hasTriedAutoGenerate.current = true;
      generateSummaryMutation.mutate();
    } else if (hasValidStoredSummary && (!messages || messages.length === 0)) {
      // We have a valid stored summary but no messages, so add the summary as a message
      console.log("📝 Using stored summary from database");
      hasTriedAutoGenerate.current = true;
      
      // Add the stored summary as the first message
      const addStoredSummary = async () => {
        try {
          let targetConversationId = conversation?.id;
          
          // Create conversation if it doesn't exist
          if (!targetConversationId) {
            console.log("Creating new conversation for stored summary");
            const conversationResponse = await apiRequest(
              'POST',
              `/api/children/${childId}/conversations`,
              { 
                title: `Analysis of ${child?.name}'s IEP Documents`,
                hasSummary: true,
                documentCountAtSummary: documents?.length || 0
              }
            );
            const newConversation = await conversationResponse.json();
            targetConversationId = newConversation.id;
          }
          
          // Add the stored summary as the first message
          await apiRequest(
            'POST',
            `/api/conversations/${targetConversationId}/messages/direct`,
            {
              content: `# Document Summary\n\n${conversation.summary}\n\nI've analyzed ${child?.name || 'your child'}'s educational documents. What questions do you have?`,
              role: 'assistant',
              timestamp: new Date().toISOString()
            }
          );
          
          // Refresh conversations list and messages
          queryClient.invalidateQueries({ 
            queryKey: [`/api/children/${childId}/conversations`] 
          });
          queryClient.invalidateQueries({ 
            queryKey: [`/api/conversations/${targetConversationId}/messages`] 
          });
          
        } catch (error) {
          console.error("Error adding stored summary:", error);
        }
      };
      
      addStoredSummary();
    } else {
      console.log("⏭️ Auto-generation skipped:", {
        isAuthenticated,
        childId: !!childId,
        documentsCount: documents?.length || 0,
        isLoadingConversations,
        hasTriedAutoGenerate: hasTriedAutoGenerate.current,
        hasConversation: !!conversation,
        conversationHasSummary: conversation?.hasSummary,
        hasValidStoredSummary,
        messagesCount: messages?.length || 0,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    isAuthenticated, 
    childId, 
    documents, 
    isLoadingConversations, 
    conversation, 
    messages
  ]);

  // Function to send a message
  const handleSendMessage = async () => {
    if (!message.trim()) return;

    const content = message.trim();
    await sendMessageMutation.mutateAsync({ content });

    // Reset state
    setMessage("");
  };

  // Manual summary generation
  const handleManualSummaryGeneration = () => {
    console.log("Manual summary generation triggered");
    generateSummaryMutation.mutate();
  };

  // Handle regenerating summary when documents are outdated
  const handleRegenerateSummary = () => {
    console.log("Regenerating outdated summary");
    generateSummaryMutation.mutate();
  };

  // Effect to scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, streamingMessage]);

  // Improved timestamp formatting function
  const formatTimestamp = (timestamp: string | Date | number) => {
    try {
      let date: Date;
      
      // Handle different timestamp formats
      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (typeof timestamp === 'string') {
        // Handle ISO strings, date strings, etc.
        date = new Date(timestamp);
      } else if (typeof timestamp === 'number') {
        // Handle Unix timestamps (both seconds and milliseconds)
        date = timestamp > 1000000000000 ? new Date(timestamp) : new Date(timestamp * 1000);
      } else {
        console.error('Invalid timestamp format:', timestamp);
        return 'Unknown time';
      }
      
      // Validate the date
      if (isNaN(date.getTime())) {
        console.error('Invalid date created from timestamp:', timestamp);
        return 'Invalid time';
      }
      
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      
      if (diffInMinutes < 1) {
        return 'Just now';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes} min ago`;
      } else if (diffInMinutes < 24 * 60) {
        const hours = Math.floor(diffInMinutes / 60);
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
      } else {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) + ', ' + 
               date.toLocaleDateString([], { month: 'short', day: 'numeric' });
      }
    } catch (error) {
      console.error('Error formatting timestamp:', error, 'Original value:', timestamp);
      return 'Unknown time';
    }
  };

  // Determine if we should show the empty state with generate button
  const showEmptyState = (
    !isMessagesLoading && 
    (!messages || messages.length === 0) &&
    documents && 
    documents.length > 0 &&
    !generateSummaryMutation.isPending &&
    (!conversation || !conversation.hasSummary)
  );

  const showNoDocumentsState = (
    !isLoadingDocuments && 
    (!documents || documents.length === 0)
  );

  if (authLoading || isChildLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Alert className="mb-4">
          <AlertDescription>
            Please log in to view this conversation.
          </AlertDescription>
        </Alert>
        <Button onClick={() => window.location.href = "/"}>
          Go to Home
        </Button>
      </div>
    );
  }

  if (!child) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Alert className="mb-4">
          <AlertDescription>
            Child profile not found.
          </AlertDescription>
        </Alert>
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <>
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex items-center text-sm text-neutral-600 mb-2">
            <Link href="/dashboard" className="text-primary hover:text-primary-dark">
              Dashboard
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link href={`/child-profile/${childId}`} className="text-primary hover:text-primary-dark">
              {child?.name || 'Child'}'s Profile
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-neutral-800">Conversation</span>
          </div>
          <h1 className="text-3xl font-bold text-neutral-900">
            Conversation with {child?.name || 'Child'}
          </h1>
          <p className="text-neutral-600 mt-1">
            Ask questions about {child?.name || 'your child'}'s educational documents
          </p>
        </div>

        {/* Outdated Summary Alert */}
        {isSummaryOutdated && (
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                New documents have been added since the last summary. Would you like to regenerate the summary with the latest documents?
              </span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRegenerateSummary}
                disabled={generateSummaryMutation.isPending}
                className="ml-4"
              >
                {generateSummaryMutation.isPending ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                    Updating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Update Summary
                  </>
                )}
              </Button>
            </AlertDescription>
          </Alert>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Sidebar */}
          <div className="md:col-span-1 space-y-4">
            {/* Child Information Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Child Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-4">
                  <div className="h-14 w-14 rounded-full overflow-hidden bg-neutral-100 mr-3 flex items-center justify-center">
                    {child.avatarUrl ? (
                      <img 
                        src={child.avatarUrl} 
                        alt={child.name} 
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          // Hide broken image and show placeholder
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    {(!child.avatarUrl || child.avatarUrl === null) && (
                      <span className="text-2xl">👤</span>
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{child.name}</h3>
                    {child.birthMonth && child.birthYear && (
                      <p className="text-sm text-neutral-500">Born: {child.birthMonth} {child.birthYear}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Documents Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Documents</CardTitle>
                <CardDescription>
                  All documents are used as context for AI responses
                </CardDescription>
              </CardHeader>
              <CardContent className="max-h-60 overflow-y-auto space-y-2">
                {isLoadingDocuments ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                  </div>
                ) : documents && documents.length > 0 ? (
                  documents.map((doc: any) => (
                    <div 
                      key={doc.id} 
                      className="p-3 rounded-md bg-neutral-50 hover:bg-neutral-100 cursor-pointer transition-colors flex items-center justify-between"
                      onClick={() => navigate(`/document-viewer/${doc.id}`)}
                    >
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-primary" />
                        <span className="font-medium text-sm truncate">{doc.title}</span>
                      </div>
                      <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full">Active</span>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-sm text-neutral-500">
                    No documents uploaded yet. 
                    <button 
                      onClick={() => setShowUploadDocumentModal(true)}
                      className="text-primary hover:underline block mt-2"
                    >
                      Upload a document
                    </button>
                  </div>
                )}
                <div className="pt-2">
                  <Button 
                    variant="outline" 
                    className="w-full text-sm" 
                    size="sm"
                    onClick={() => setShowUploadDocumentModal(true)}
                  >
                    <Pencil className="h-3 w-3 mr-1" />
                    Upload Document
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Main Conversation Area */}
          <div className="md:col-span-2">
            <Card className="h-[700px] flex flex-col">
              <CardHeader className="pb-2 border-b">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Conversation</CardTitle>
                    <CardDescription>
                      About: {child.name}'s Educational Documents
                      {documents && documents.length > 0 && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                          {documents.length} document{documents.length !== 1 ? 's' : ''} as context
                        </span>
                      )}
                    </CardDescription>
                  </div>
                  {documents && documents.length > 0 && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                    >
                      <Link href={`/document-viewer?childId=${childId}`}>
                        <FileText className="h-4 w-4 mr-2" />
                        View Documents
                      </Link>
                    </Button>
                  )}
                </div>
              </CardHeader>
              
              {/* Messages Area */}
              <CardContent className="flex-grow overflow-y-auto pt-4 pb-1">
                {isMessagesLoading || generateSummaryMutation.isPending ? (
                  <div className="flex justify-center items-center h-full">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
                      <p className="text-sm text-neutral-500">
                        {generateSummaryMutation.isPending ? 'Generating document summary...this may take a while...' : 'Loading messages...'}
                      </p>
                    </div>
                  </div>
                ) : messages && messages.length > 0 ? (
                  <div className="space-y-6">
                    {messages.map((msg: any) => (
                      <div 
                        key={msg.id} 
                        className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div 
                          className={`max-w-[85%] lg:max-w-[75%] px-5 py-4 rounded-lg shadow-sm group relative ${
                            msg.role === 'user' 
                              ? 'bg-primary text-white rounded-tr-none' 
                              : 'bg-white border border-gray-200 text-neutral-800 rounded-tl-none'
                          }`}
                        >
                          {/* Copy button for AI messages */}
                          {msg.role === 'assistant' && (
                            <button
                              onClick={() => copyMessage(msg.id, msg.content)}
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1.5 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary/50"
                              title="Copy message"
                            >
                              {copiedMessageId === msg.id ? (
                                <Check className="h-4 w-4 text-green-600" />
                              ) : (
                                <Copy className="h-4 w-4 text-gray-500" />
                              )}
                            </button>
                          )}

                          {/* Main content with prose styling */}
                          <div className={`${
                            msg.role === 'user' 
                              ? 'prose prose-sm prose-invert max-w-none prose-headings:text-white prose-strong:text-white prose-li:text-white prose-p:text-white' 
                              : 'prose prose-sm max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-li:text-gray-700 prose-strong:text-gray-900'
                          } prose-headings:mb-3 prose-p:mb-3 prose-li:mb-1 prose-ol:mb-4 prose-ul:mb-4`}>
                            <ReactMarkdown>{msg.content}</ReactMarkdown>
                          </div>
                          
                          {/* Timestamp and Copy Button Row */}
                          <div className={`flex items-center justify-between mt-3 pt-2 border-t ${
                            msg.role === 'user' 
                              ? 'border-primary-foreground/20' 
                              : 'border-gray-100'
                          }`}>
                            <div className={`text-xs ${
                              msg.role === 'user' 
                                ? 'text-primary-foreground/70' 
                                : 'text-neutral-500'
                            }`}>
                              {formatTimestamp(msg.createdAt || msg.timestamp || msg.created_at)}
                            </div>
                            
                            {/* Bottom copy button for AI messages */}
                            {msg.role === 'assistant' && (
                              <button
                                onClick={() => copyMessage(msg.id, msg.content)}
                                className={`flex items-center gap-1 text-xs px-2 py-1 rounded transition-colors ${
                                  copiedMessageId === msg.id
                                    ? 'text-green-600 bg-green-50'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                                }`}
                                title="Copy message"
                              >
                                {copiedMessageId === msg.id ? (
                                  <>
                                    <Check className="h-3 w-3" />
                                    Copied!
                                  </>
                                ) : (
                                  <>
                                    <Copy className="h-3 w-3" />
                                    Copy
                                  </>
                                )}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {/* Show streaming message if it exists */}
                    {streamingMessage && (
                      <div className="flex justify-start">
                        <div className="max-w-[85%] lg:max-w-[75%] px-5 py-4 rounded-lg shadow-sm bg-white border border-gray-200 text-neutral-800 rounded-tl-none group relative">
                          {/* Copy button for streaming message (only when complete) */}
                          {!streamingMessage.isStreaming && (
                            <button
                              onClick={() => copyMessage('streaming', streamingMessage.content)}
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1.5 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary/50"
                              title="Copy message"
                            >
                              {copiedMessageId === 'streaming' ? (
                                <Check className="h-4 w-4 text-green-600" />
                              ) : (
                                <Copy className="h-4 w-4 text-gray-500" />
                              )}
                            </button>
                          )}

                          {/* Streaming content with typing animation */}
                          <div className="prose prose-sm max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-li:text-gray-700 prose-strong:text-gray-900 prose-headings:mb-3 prose-p:mb-3 prose-li:mb-1 prose-ol:mb-4 prose-ul:mb-4">
                            <ReactMarkdown>{streamingMessage.content}</ReactMarkdown>
                            {/* Show typing cursor while streaming */}
                            {streamingMessage.isStreaming && (
                              <span className="inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse" />
                            )}
                          </div>
                          
                          {/* Timestamp */}
                          <div className="text-xs mt-3 pt-2 border-t text-neutral-500 border-gray-100">
                            {streamingMessage.isStreaming ? 'Responding...' : formatTimestamp(streamingMessage.timestamp)}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div ref={messagesEndRef} />
                  </div>
                ) : showNoDocumentsState ? (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <FileText className="h-16 w-16 text-neutral-300 mb-4" />
                    <h3 className="text-lg font-semibold text-neutral-600 mb-2">No Documents Yet</h3>
                    <p className="text-neutral-500 mb-4 max-w-md">
                      Upload some educational documents for {child.name} to start having AI-powered conversations about their IEP, reports, and progress.
                    </p>
                    <Button onClick={() => setShowUploadDocumentModal(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Upload First Document
                    </Button>
                  </div>
                ) : showEmptyState ? (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <MessageSquare className="h-16 w-16 text-neutral-300 mb-4" />
                    <h3 className="text-lg font-semibold text-neutral-600 mb-2">Ready to Analyze Documents</h3>
                    <p className="text-neutral-500 mb-4 max-w-md">
                      I can analyze {child.name}'s {documents?.length} document{documents?.length !== 1 ? 's' : ''} and provide insights about their educational plan.
                    </p>
                    <div className="space-y-2">
                      <Button 
                        onClick={handleManualSummaryGeneration}
                        disabled={generateSummaryMutation.isPending}
                        className="mb-2"
                      >
                        {generateSummaryMutation.isPending ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Generating Summary, this may take a while...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Generate Document Summary
                          </>
                        )}
                      </Button>
                      <p className="text-xs text-neutral-400">
                        Or start typing a question below
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <p className="text-neutral-500">
                      Start a conversation about {child.name}'s educational documents.
                    </p>
                  </div>
                )}
              </CardContent>
              
              {/* Input Area */}
              <div className="p-4 border-t">
                <div className="flex">
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder={
                      documents && documents.length > 0
                        ? `Ask about ${child.name}'s educational documents...`
                        : "Upload documents first to start chatting..."
                    }
                    className="flex-grow resize-none"
                    disabled={!documents || documents.length === 0 || isStreaming}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    className="ml-2 self-end"
                    disabled={
                      sendMessageMutation.isPending || 
                      isStreaming ||
                      !message.trim() ||
                      !documents || 
                      documents.length === 0
                    }
                  >
                    {sendMessageMutation.isPending || isStreaming ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </Card>
            <div className="mt-4 mb-2 p-4 bg-yellow-100 border-l-4 border-yellow-400 text-yellow-800 rounded">
              <strong>Warning:</strong> IEPs.ai can make mistakes. Please double-check responses.
            </div>
          </div>
        </div>
      </main>

      {/* Upload Document Modal */}
      <Dialog open={showUploadDocumentModal} onOpenChange={setShowUploadDocumentModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload IEP Document</DialogTitle>
          </DialogHeader>
          <form 
            onSubmit={(e) => {
              e.preventDefault();
              
              if (!childId) {
                toast({
                  title: "Error",
                  description: "No child selected. Please try again.",
                  variant: "destructive"
                });
                return;
              }
              
              if (!documentFile) {
                toast({
                  title: "Missing document",
                  description: "Please select a file to upload.",
                  variant: "destructive"
                });
                return;
              }
              
              uploadDocumentMutation.mutate();
            }} 
            className="space-y-4 mt-4"
          >
            <div>
              <Label htmlFor="documentTitle">Document Title</Label>
              <Input 
                id="documentTitle" 
                value={documentTitle} 
                onChange={(e) => setDocumentTitle(e.target.value)} 
                placeholder="E.g., Spring 2024 IEP"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="documentType">Document Type</Label>
              <select 
                id="documentType"
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
              >
                <option value="IEP">IEP</option>
                <option value="Evaluation">Evaluation</option>
                <option value="Progress Report">Progress Report</option>
                <option value="Other">Other</option>
              </select>
            </div>
            
            <div>
              <Label htmlFor="documentSchoolYear">School Year</Label>
              <Input 
                id="documentSchoolYear" 
                value={documentSchoolYear} 
                onChange={(e) => setDocumentSchoolYear(e.target.value)} 
                placeholder="E.g., 2023-2024"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="documentFile">Document File</Label>
              <Input
                id="documentFile"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];
                    setDocumentFile(file);
                    
                    // Auto-fill title if empty
                    if (!documentTitle) {
                      setDocumentTitle(file.name.split('.')[0]);
                    }
                  }
                }}
                className="mt-1 cursor-pointer border-0 px-0 py-0 file:cursor-pointer file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark"
                required
              />
              <p className="text-xs text-neutral-500 mt-1">
                Upload an IEP document, evaluation, or related file (PDF, Word, or Text).
              </p>
            </div>
            
            <div className="flex space-x-2 pt-2">
              <Button 
                type="button" 
                variant="outline" 
                className="flex-1"
                onClick={() => setShowUploadDocumentModal(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1 bg-primary hover:bg-primary-dark"
                disabled={uploadDocumentMutation.isPending}
              >
                {uploadDocumentMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  "Upload Document"
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
      
      <FeedbackWidget page="Conversation" />
    </>
  );
}