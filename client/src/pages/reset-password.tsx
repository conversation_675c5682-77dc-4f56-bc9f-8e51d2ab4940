import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function ResetPassword() {
  const [, navigate] = useLocation();
  
  // Form state
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  
  // Get token from URL
  const [token, setToken] = useState("");

  useEffect(() => {
    // Extract token from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const resetToken = urlParams.get('token');
    
    if (!resetToken) {
      setError("Invalid or missing reset token. Please request a new password reset.");
    } else {
      setToken(resetToken);
    }
  }, []);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setPasswordErrors([]);

    // Validate passwords match
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (!token) {
      setError("Invalid reset token");
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          newPassword: password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigate("/");
        }, 3000);
      } else {
        if (data.errors) {
          setPasswordErrors(data.errors);
        } else {
          setError(data.message || "Failed to reset password");
        }
      }
    } catch (error) {
      console.error("Reset password error:", error);
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToHome = () => {
    navigate("/");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-neutral-900">
            {success ? "Password Reset Successful!" : "Reset Your Password"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {success ? (
            <div className="text-center space-y-4">
              <div className="p-4 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
                Your password has been successfully reset. You can now log in with your new password.
              </div>
              <p className="text-sm text-neutral-600">
                Redirecting to homepage in 3 seconds...
              </p>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={handleBackToHome}
              >
                Go to Homepage Now
              </Button>
            </div>
          ) : (
            <>
              {!token ? (
                <div className="text-center space-y-4">
                  <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                    {error}
                  </div>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleBackToHome}
                  >
                    Return to Homepage
                  </Button>
                </div>
              ) : (
                <>
                  <p className="text-sm text-neutral-600 text-center mb-6">
                    Enter your new password below. Make sure it meets our security requirements.
                  </p>

                  {error && (
                    <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md mb-4">
                      {error}
                    </div>
                  )}

                  {passwordErrors.length > 0 && (
                    <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md mb-4">
                      <p className="font-medium mb-1">Password requirements not met:</p>
                      <ul className="list-disc list-inside space-y-1">
                        {passwordErrors.map((err, index) => (
                          <li key={index}>{err}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <form onSubmit={handleResetPassword} className="space-y-4">
                    <div>
                      <Label htmlFor="new-password">New Password</Label>
                      <Input 
                        id="new-password" 
                        type="password" 
                        placeholder="Enter your new password" 
                        className="mt-1"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                      <p className="text-xs text-neutral-500 mt-1">
                        Must be at least 8 characters with uppercase, lowercase, and number
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input 
                        id="confirm-password" 
                        type="password" 
                        placeholder="Confirm your new password" 
                        className="mt-1"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full bg-primary hover:bg-primary-dark"
                      disabled={isLoading || !password || !confirmPassword}
                    >
                      {isLoading ? "Resetting Password..." : "Reset Password"}
                    </Button>
                  </form>

                  <div className="mt-4 text-center">
                    <Button 
                      variant="link" 
                      className="text-neutral-600 hover:text-neutral-800 text-sm"
                      onClick={handleBackToHome}
                    >
                      Back to Homepage
                    </Button>
                  </div>
                </>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}