import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams } from "wouter";
import { toast } from "@/hooks/use-toast";
import { Pencil, ChevronRight } from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { queryClient } from "@/lib/queryClient";
import { Link } from "wouter";
import FeedbackWidget from "@/components/FeedbackWidget";

export default function ChildProfile() {
  const { id: childId } = useParams();
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(new Set());
  
  // Profile editing states
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingBirthMonth, setIsEditingBirthMonth] = useState(false);
  const [isEditingBirthYear, setIsEditingBirthYear] = useState(false);
  const [editedName, setEditedName] = useState("");
  const [editedBirthMonth, setEditedBirthMonth] = useState("");
  const [editedBirthYear, setEditedBirthYear] = useState("");
  
  // Avatar editing states
  const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);

  const { data: child, isLoading: isLoadingChild } = useQuery({
    queryKey: [`/api/children/${childId}`],
    enabled: !!childId
  });

  const { data: documents, isLoading: isLoadingDocuments } = useQuery({
    queryKey: [`/api/children/${childId}/documents`],
    enabled: !!childId
  });

  // Update child mutation
  const updateChildMutation = useMutation({
    mutationFn: async (updates: { name?: string; birthMonth?: string; birthYear?: string }) => {
      const response = await fetch(`/api/children/${childId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        throw new Error("Failed to update child");
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/children/${childId}`] });
      toast({
        title: "Profile updated",
        description: "Your changes have been saved successfully."
      });
    },
    onError: () => {
      toast({
        title: "Failed to update",
        description: "There was a problem saving your changes.",
        variant: "destructive"
      });
    }
  });

  // Initialize edit states when child data is loaded
  useEffect(() => {
    if (child) {
      setEditedName(child.name);
      setEditedBirthMonth(child.birthMonth || "");
      setEditedBirthYear(child.birthYear || "");
    }
  }, [child]);

  // Handler for avatar file selection
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedAvatar(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handler for avatar upload
  const handleAvatarUpload = async () => {
    if (!selectedAvatar) return;
    
    setIsUploadingAvatar(true);
    
    try {
      const formData = new FormData();
      formData.append('avatar', selectedAvatar);
      
      const response = await fetch(`/api/children/${childId}/avatar`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload avatar');
      }
      
      const result = await response.json();
      
      // Refresh child data to get updated avatar URL
      queryClient.invalidateQueries({ queryKey: [`/api/children/${childId}`] });
      
      // Also refresh dashboard to update avatar there
      queryClient.invalidateQueries({ queryKey: ['/api/children'] });
      
      // Reset avatar state
      setSelectedAvatar(null);
      setAvatarPreview(null);
      
      toast({
        title: "Avatar updated successfully!",
        description: "Your profile picture has been updated and will appear on the dashboard."
      });
      
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast({
        title: "Failed to upload avatar",
        description: "There was a problem uploading your profile picture. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  if (isLoadingChild) {
    return <div>Loading...</div>;
  }

  if (!child) {
    return <div>Child not found</div>;
  }

  const handleDeleteDocument = async (doc: any) => {
    const confirmMessage = `Are you sure you want to delete "${doc.title}"?\n\nThis action cannot be undone.`;

    if (confirm(confirmMessage)) {
      setDeletingDocuments(prev => new Set(prev).add(doc.id));

      try {
        const response = await fetch(`/api/documents/${doc.id}`, {
          method: "DELETE",
          credentials: "include"
        });

        const responseData = await response.json();

        if (!response.ok) {
          throw new Error(responseData.message || "Failed to delete document");
        }

        toast({
          title: "Document deleted",
          description: `${doc.title} has been deleted successfully.`
        });

        // Refresh documents list
        await queryClient.invalidateQueries({
          queryKey: [`/api/children/${childId}/documents`]
        });
      } catch (error: any) {
        toast({
          title: "Failed to delete document",
          description: error.message,
          variant: "destructive"
        });
      } finally {
        setDeletingDocuments(prev => {
          const newSet = new Set(prev);
          newSet.delete(doc.id);
          return newSet;
        });
      }
    }
  };

  const handleSaveProfile = async () => {
    const updates: { name?: string; birthMonth?: string; birthYear?: string } = {};
    
    if (editedName !== child.name) updates.name = editedName;
    if (editedBirthMonth !== child.birthMonth) updates.birthMonth = editedBirthMonth;
    if (editedBirthYear !== child.birthYear) updates.birthYear = editedBirthYear;
    
    if (Object.keys(updates).length > 0) {
      await updateChildMutation.mutateAsync(updates);
    }
    
    setIsEditingName(false);
    setIsEditingBirthMonth(false);
    setIsEditingBirthYear(false);
  };

  return (
    <>
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex items-center text-sm text-neutral-600 mb-2">
            <Link href="/dashboard" className="text-primary hover:text-primary-dark">
              Dashboard
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link href={`/child-profile/${childId}`} className="text-primary hover:text-primary-dark">
              {child.name}'s Profile
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-neutral-900">{child.name}'s Profile</h1>
          <p className="text-neutral-600 mt-1">Manage documents and track progress</p>
        </div>

        <Tabs defaultValue="profile" className="space-y-4">
          <TabsList>
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  {/* Avatar Field */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Profile Picture</Label>
                      <div className="flex items-center space-x-4">
                        <div className="h-16 w-16 rounded-full overflow-hidden border border-neutral-200">
                          {avatarPreview ? (
                            <img 
                              src={avatarPreview} 
                              alt="Avatar preview" 
                              className="h-full w-full object-cover"
                            />
                          ) : child.avatarUrl ? (
                            <img 
                              src={child.avatarUrl} 
                              alt={child.name} 
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                // Hide broken image and show placeholder
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          {(!avatarPreview && (!child.avatarUrl || child.avatarUrl === null)) && (
                            <div className="h-full w-full bg-neutral-100 flex items-center justify-center text-neutral-400">
                              <span className="text-2xl">👤</span>
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col space-y-2">
                          <div className="relative">
                            <Input
                              type="file"
                              accept="image/*"
                              onChange={handleAvatarChange}
                              className="max-w-xs cursor-pointer file:cursor-pointer file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark border-0 p-0"
                            />
                          </div>
                          {selectedAvatar && (
                            <Button 
                              size="sm"
                              onClick={handleAvatarUpload}
                              disabled={isUploadingAvatar}
                              className="bg-primary hover:bg-primary-dark text-white"
                            >
                              {isUploadingAvatar ? "Uploading..." : "Upload Avatar"}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Name Field */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Name</Label>
                      {isEditingName ? (
                        <div className="flex gap-2">
                          <Input
                            value={editedName}
                            onChange={(e) => setEditedName(e.target.value)}
                            className="max-w-xs"
                          />
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setEditedName(child.name);
                              setIsEditingName(false);
                            }}
                          >
                            Cancel
                          </Button>
                          <Button 
                            size="sm"
                            onClick={handleSaveProfile}
                          >
                            Save
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <p className="text-lg">{child.name}</p>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsEditingName(true)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Birth Month Field */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Birth Month</Label>
                      {isEditingBirthMonth ? (
                        <div className="flex gap-2">
                          <select
                            value={editedBirthMonth}
                            onChange={(e) => setEditedBirthMonth(e.target.value)}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 max-w-xs"
                          >
                            <option value="">Select Month</option>
                            <option value="January">January</option>
                            <option value="February">February</option>
                            <option value="March">March</option>
                            <option value="April">April</option>
                            <option value="May">May</option>
                            <option value="June">June</option>
                            <option value="July">July</option>
                            <option value="August">August</option>
                            <option value="September">September</option>
                            <option value="October">October</option>
                            <option value="November">November</option>
                            <option value="December">December</option>
                          </select>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setEditedBirthMonth(child.birthMonth || "");
                              setIsEditingBirthMonth(false);
                            }}
                          >
                            Cancel
                          </Button>
                          <Button 
                            size="sm"
                            onClick={handleSaveProfile}
                          >
                            Save
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <p className="text-lg">{child.birthMonth || "Not set"}</p>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsEditingBirthMonth(true)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Birth Year Field */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Birth Year</Label>
                      {isEditingBirthYear ? (
                        <div className="flex gap-2">
                          <select
                            value={editedBirthYear}
                            onChange={(e) => setEditedBirthYear(e.target.value)}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 max-w-xs"
                          >
                            <option value="">Select Year</option>
                            {Array.from({ length: 20 }, (_, i) => {
                              const year = new Date().getFullYear() - i;
                              return (
                                <option key={year} value={year.toString()}>
                                  {year}
                                </option>
                              );
                            })}
                          </select>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setEditedBirthYear(child.birthYear || "");
                              setIsEditingBirthYear(false);
                            }}
                          >
                            Cancel
                          </Button>
                          <Button 
                            size="sm"
                            onClick={handleSaveProfile}
                          >
                            Save
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <p className="text-lg">{child.birthYear || "Not set"}</p>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsEditingBirthYear(true)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Documents</h2>
              <Button asChild>
                <a href={`/document-upload/${childId}`}>Upload Document</a>
              </Button>
            </div>

            {isLoadingDocuments ? (
              <div>Loading documents...</div>
            ) : !documents || documents.length === 0 ? (
              <div className="text-center py-8 text-neutral-500">
                No documents uploaded yet
              </div>
            ) : (
              <div className="grid gap-4">
                {documents.map((doc: any) => (
                  <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">{doc.title}</h3>
                      <p className="text-sm text-neutral-500">
                        Uploaded {new Date(doc.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" asChild>
                        <a href={`/document-viewer/${doc.id}`}>View</a>
                      </Button>
                      <Button
                        variant="destructive"
                        disabled={deletingDocuments.has(doc.id)}
                        onClick={() => handleDeleteDocument(doc)}
                      >
                        {deletingDocuments.has(doc.id) ? "Deleting..." : "Delete"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="progress">
            <div className="text-center py-8 text-neutral-500">
              Progress tracking coming soon
            </div>
          </TabsContent>
        </Tabs>
      </main>
      
      <FeedbackWidget page="Child Profile" />
    </>
  );
}
