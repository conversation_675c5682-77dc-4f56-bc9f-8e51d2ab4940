import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import Hero from "@/components/home/<USER>";
import PersonalStory from "@/components/home/<USER>";
import NotAlone from "@/components/home/<USER>";
import Benefits from "@/components/home/<USER>";
import HowItWorks from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import FAQ from "@/components/home/<USER>";
import CallToAction from "@/components/home/<USER>";
import SignupModal from "@/components/modals/SignupModal";
import LoginModal from "@/components/modals/LoginModal";
import BetaBanner from "@/components/ui/beta-banner";
import FeedbackWidget from "@/components/FeedbackWidget";

export default function HomePage() {
  const { isAuthenticated } = useAuth();
  const [, navigate] = useLocation();
  
  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);
  // Handle smooth scrolling for anchor links
  const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    const href = e.currentTarget.getAttribute('href');
    if (href?.startsWith('#')) {
      e.preventDefault();
      const targetId = href;
      const targetElement = document.querySelector(targetId);
      
      if (targetElement) {
        window.scrollTo({
          top: targetElement.getBoundingClientRect().top + window.scrollY - 120, // Account for fixed header with extra padding
          behavior: 'smooth',
        });
      }
    }
  };

  // Add event listeners for anchor links after component mounts
  useState(() => {
    const anchors = document.querySelectorAll('a[href^="#"]');
    anchors.forEach(anchor => {
      anchor.addEventListener('click', handleAnchorClick as any);
    });

    return () => {
      anchors.forEach(anchor => {
        anchor.removeEventListener('click', handleAnchorClick as any);
      });
    };
  });

  return (
    <>
      <BetaBanner />
      <Navbar />
      <main>
        <Hero />
        <PersonalStory />
        <NotAlone />
        <Benefits />
        <HowItWorks />
        <Testimonials />
        <FAQ />
        <CallToAction />
      </main>
      
      <FeedbackWidget page="Home" />
      <Footer />
    </>
  );
}
