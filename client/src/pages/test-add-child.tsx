import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "wouter";

export default function TestAddChild() {
  const { toast } = useToast();
  const [childName, setChildName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!childName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a child name",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // Simple direct fetch to test the API
      const res = await fetch('/api/children', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name: childName })
      });

      const data = await res.json();
      setResult(data);
      
      if (res.ok) {
        toast({
          title: "Success",
          description: "Child added successfully"
        });
        setChildName("");
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to add child",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error adding child:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="mb-6">
        <Link href="/dashboard" className="text-primary hover:underline">
          &larr; Back to Dashboard
        </Link>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Test Add Child</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="childName">Child's Name</Label>
              <Input 
                id="childName"
                value={childName}
                onChange={(e) => setChildName(e.target.value)}
                placeholder="Enter child's name"
              />
            </div>
            
            <Button 
              type="submit" 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? "Adding Child..." : "Add Child"}
            </Button>
          </form>
          
          {result && (
            <div className="mt-6 p-4 bg-neutral-50 rounded-md">
              <h3 className="font-medium mb-2">API Response:</h3>
              <pre className="text-xs overflow-auto p-2 bg-neutral-100 rounded">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}