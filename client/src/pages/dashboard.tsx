import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link, useLocation } from "wouter";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, FileText, Users, Settings, Upload, User,
  ChevronDown, ChevronRight, MoreHorizontal, MessageSquare,
  Trash2, File, Upload as UploadIcon, Edit, Loader2, CheckCircle
} from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DeleteChildDialog } from "@/components/DeleteChildDialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import FeedbackWidget from "@/components/FeedbackWidget";

export default function Dashboard() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const [isAddChildDialogOpen, setIsAddChildDialogOpen] = useState(false);
  const [newChildName, setNewChildName] = useState("");
  const [birthMonth, setBirthMonth] = useState("");
  const [birthYear, setBirthYear] = useState("");
  const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [iepDocument, setIepDocument] = useState<File | null>(null);
  const [activeTab, setActiveTab] = useState("children");
  
  // Document management states
  const [activeChildId, setActiveChildId] = useState<string | null>(null);
  const [showUploadDocumentModal, setShowUploadDocumentModal] = useState(false);
  const [showDocumentListModal, setShowDocumentListModal] = useState(false);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [documentTitle, setDocumentTitle] = useState("");
  const [documentType, setDocumentType] = useState("IEP");
  const [documentSchoolYear, setDocumentSchoolYear] = useState("");


  // Settings state
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [isSavingSettings, setIsSavingSettings] = useState(false);

  // Remove edit profile modal states - we'll navigate to profile page instead

  // Redirect to home if not authenticated
  if (!isLoading && !isAuthenticated) {
    navigate('/');
    return null;
  }

  // Query for user's children (with state management)
  const [localChildren, setLocalChildren] = useState<any[]>([]);
  const { data: children, isLoading: isLoadingChildren } = useQuery({
    queryKey: ['/api/children'],
    enabled: isAuthenticated
  });
  
  // Update local children whenever data changes from API
  useEffect(() => {
    if (children && Array.isArray(children)) {
      setLocalChildren(children);
    }
  }, [children]);

  // Initialize settings state when user data is loaded
  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || "");
      setLastName(user.lastName || "");
    }
  }, [user]);

  // Query for family members
  const { data: familyMembers, isLoading: isLoadingFamilyMembers } = useQuery({
    queryKey: ['/api/family-members'],
    enabled: isAuthenticated,
  });

  // Query for pending family invitations
  const { data: familyInvitations, isLoading: isLoadingFamilyInvitations } = useQuery({
    queryKey: ['/api/family-invitations'],
    enabled: isAuthenticated,
  });

  // Handler for avatar file selection
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedAvatar(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handler for IEP document upload
  const handleIepDocumentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setIepDocument(e.target.files[0]);
    }
  };

  // Function to navigate to child profile page (same as Profile button)
  const handleEditProfile = (child: any) => {
    navigate(`/child-profile/${child.id}`);
  };

  // Function to open manage documents modal
  const handleManageDocuments = (childId: string) => {
    navigate(`/document-viewer?childId=${childId}`);
  };

  // Mutation to update a child's profile - removed since we navigate to profile page

  // Mutation to delete a child
  const [childToDelete, setChildToDelete] = useState<{ id: string; name: string } | null>(null);
  const deleteChildMutation = useMutation({
    mutationFn: async (childId: string) => {
      const response = await apiRequest('DELETE', `/api/children/${childId}`, undefined);
      return response.json();
    },
    onSuccess: (_, childId) => {
      // Remove from local state
      setLocalChildren(prev => prev.filter(child => child.id !== childId));
      
      // Refresh children data
      queryClient.invalidateQueries({ queryKey: ['/api/children'] });
      
      toast({
        title: "Child removed",
        description: "The child's profile and all related data have been deleted."
      });
    },
    onError: (error) => {
      console.error("Error deleting child:", error);
      toast({
        title: "Failed to delete",
        description: "There was a problem deleting the child's profile.",
        variant: "destructive"
      });
    }
  });

  // Handle invite family member form submission
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteFirstName, setInviteFirstName] = useState("");
  const [inviteLastName, setInviteLastName] = useState("");

  // Mutation to invite family member
  const inviteFamilyMemberMutation = useMutation({
    mutationFn: async (data: { email: string; firstName: string; lastName?: string }) => {
      const response = await apiRequest('POST', '/api/family-members/invite', data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/family-members'] });
      queryClient.invalidateQueries({ queryKey: ['/api/family-invitations'] });
      // Reset form fields
      setInviteEmail("");
      setInviteFirstName("");
      setInviteLastName("");
      // Show success toast
      toast({
        title: "Invitation Sent!",
        description: "Your family member has been invited to join your workspace.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to send invitation",
        description: error instanceof Error ? error.message : "There was a problem sending the invitation. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Handle add child form submission
  const handleAddChild = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newChildName.trim()) {
      toast({
        title: "Missing information",
        description: "Please enter your child's name.",
        variant: "destructive"
      });
      return;
    }
    
    try {
      console.log("Submitting child data:", { name: newChildName, birthMonth, birthYear });
      
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('name', newChildName);
      formData.append('birthMonth', birthMonth);
      formData.append('birthYear', birthYear);
      
      if (selectedAvatar) {
        formData.append('avatar', selectedAvatar);
      }
      
      // First, create a temporary child to show immediately
      const tempChild = {
        id: "temp-" + Math.random().toString(36).substring(2, 15),
        name: newChildName,
        birthMonth: birthMonth,
        birthYear: birthYear,
        createdAt: new Date().toISOString()
      };
      
      // Update UI immediately with the temp child
      setLocalChildren(prev => [...prev, tempChild]);
      
      // Then actually submit to the API with FormData
      const response = await fetch('/api/children', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error("Failed to save child to database");
      }
      
      const savedChild = await response.json();
      console.log("Child saved to database:", savedChild);
      
      // Replace the temp child with the saved one
      setLocalChildren(prev => 
        prev.map(child => child.id === tempChild.id ? savedChild : child)
      );
      
      // Reset form fields
      setNewChildName("");
      setBirthMonth("");
      setBirthYear("");
      setSelectedAvatar(null);
      setAvatarPreview(null);
      setIepDocument(null);
      setIsAddChildDialogOpen(false);
      
      // Refresh children data from server
      queryClient.invalidateQueries({ queryKey: ['/api/children'] });
      
      toast({
        title: "Success!",
        description: "Your child's profile has been created and saved."
      });
    } catch (error) {
      console.error("Error adding child:", error);
      toast({
        title: "Error adding child",
        description: error instanceof Error ? error.message : "There was a problem adding your child.",
        variant: "destructive"
      });
    }
  };

  const handleInviteFamilyMember = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inviteEmail.trim()) {
      toast({
        title: "Missing email",
        description: "Please enter an email address.",
        variant: "destructive"
      });
      return;
    }
    
    inviteFamilyMemberMutation.mutate({
      email: inviteEmail,
      firstName: inviteFirstName,
      lastName: inviteLastName
    });
  };

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!firstName.trim() || !lastName.trim()) {
      toast({
        title: "Missing information",
        description: "Please enter both first and last name.",
        variant: "destructive"
      });
      return;
    }
    
    setIsSavingSettings(true);
    
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          firstName: firstName.trim(),
          lastName: lastName.trim()
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to update profile');
      }
      
      // Show success message
      toast({
        title: "Settings saved",
        description: "Your profile has been updated successfully."
      });
      
      // Refresh user data
      queryClient.invalidateQueries({ queryKey: ['/api/user'] });
      
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Failed to save",
        description: "There was a problem saving your changes. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSavingSettings(false);
    }
  };

  // Mutation to upload a document
  const uploadDocumentMutation = useMutation({
    mutationFn: async () => {
      if (!activeChildId || !documentFile) return null;
      
      const formData = new FormData();
      // Important: Use 'document' as the field name to match server expectations
      formData.append('document', documentFile);
      formData.append('title', documentTitle || documentFile.name);
      formData.append('documentType', documentType);
      formData.append('schoolYear', documentSchoolYear || '');
      
      console.log("Upload starting for child ID:", activeChildId);
      console.log("Document title:", documentTitle || documentFile.name);
      
      try {
        // Use the correct endpoint format that matches the server implementation
        const response = await fetch(`/api/children/${activeChildId}/documents/upload`, {
          method: 'POST',
          body: formData,
          // No need to set Content-Type as it's automatically set for FormData
        });
        
        console.log("Upload response status:", response.status);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error("Upload error response:", errorData);
          throw new Error(errorData.message || "Failed to upload document");
        }
        
        const responseData = await response.json();
        console.log("Upload success response:", responseData);
        return responseData;
      } catch (err) {
        console.error("Error in document upload:", err);
        throw err;
      }
    },
    onSuccess: (data) => {
      console.log("Upload successful:", data);
      
      // Reset form fields
      setDocumentFile(null);
      setDocumentTitle("");
      setDocumentType("IEP");
      setDocumentSchoolYear("");
      setShowUploadDocumentModal(false);
      
      // Refresh children data
      queryClient.invalidateQueries({ queryKey: ['/api/children'] });
      
      toast({
        title: "Document uploaded",
        description: "Your document has been uploaded successfully."
      });
      
      // Navigate to document viewer with the correct ID
      if (data && data.id) {
        // Use a short delay to ensure the state updates properly before navigation
        setTimeout(() => {
          console.log("Navigating to document viewer with ID:", data.id);
          navigate(`/document-viewer/${data.id}`);
        }, 300);
      } else {
        console.error("No document ID received in response");
      }
    },
    onError: (error) => {
      console.error("Error uploading document:", error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "There was a problem uploading your document. Please try again.",
        variant: "destructive"
      });
    }
  });
  
  // Mutation to delete a document
  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: string) => {
      const response = await apiRequest('DELETE', `/api/documents/${documentId}`, undefined);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/children'] });
      toast({
        title: "Document deleted",
        description: "The document has been permanently deleted."
      });
    },
  });

  return (
    <>
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-neutral-900">Family Workspace</h1>
            <p className="text-neutral-600 mt-1">Manage your children's IEPs and family members</p>
          </div>
          <div className="flex space-x-4">
            <Dialog open={isAddChildDialogOpen} onOpenChange={setIsAddChildDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary-dark">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Child
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add a Child</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleAddChild} className="space-y-4 mt-4">
                  {/* Child's name */}
                  <div>
                    <Label htmlFor="childName">Child's Name or Nickname</Label>
                    <Input 
                      id="childName" 
                      value={newChildName} 
                      onChange={(e) => setNewChildName(e.target.value)} 
                      placeholder="Enter child's name"
                      className="mt-1"
                      required
                    />
                  </div>
                  
                  {/* Birth Month and Year */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="birthMonth">Birth Month</Label>
                      <select 
                        id="birthMonth"
                        value={birthMonth}
                        onChange={(e) => setBirthMonth(e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                      >
                        <option value="">Select Month</option>
                        <option value="January">January</option>
                        <option value="February">February</option>
                        <option value="March">March</option>
                        <option value="April">April</option>
                        <option value="May">May</option>
                        <option value="June">June</option>
                        <option value="July">July</option>
                        <option value="August">August</option>
                        <option value="September">September</option>
                        <option value="October">October</option>
                        <option value="November">November</option>
                        <option value="December">December</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="birthYear">Birth Year</Label>
                      <select 
                        id="birthYear"
                        value={birthYear}
                        onChange={(e) => setBirthYear(e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                      >
                        <option value="">Select Year</option>
                        {Array.from({ length: 20 }, (_, i) => {
                          const year = new Date().getFullYear() - i;
                          return (
                            <option key={year} value={year.toString()}>
                              {year}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  </div>
                  
                  {/* Avatar Upload */}
                  <div>
                    <Label htmlFor="avatar">Profile Picture (Optional)</Label>
                    <div className="mt-1 flex items-center space-x-4">
                      {avatarPreview ? (
                        <div className="h-16 w-16 rounded-full overflow-hidden border border-neutral-200">
                          <img 
                            src={avatarPreview} 
                            alt="Avatar preview" 
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="h-16 w-16 rounded-full bg-neutral-100 flex items-center justify-center text-neutral-400 border border-neutral-200">
                          <span className="text-2xl">👤</span>
                        </div>
                      )}
                      <Input
                        id="avatar"
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarChange}
                        className="max-w-xs cursor-pointer file:cursor-pointer file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark border-0 p-0"
                      />
                    </div>
                  </div>
                  
                  {/* IEP Document Upload removed as requested */}
                  
                  <Button 
                    type="submit" 
                    className={`w-full ${newChildName && birthMonth && birthYear ? 'bg-green-600 hover:bg-green-700' : 'bg-primary hover:bg-primary-dark'}`}
                    disabled={!newChildName.trim() || !birthMonth || !birthYear}
                  >
                    Add Child
                  </Button>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 max-w-md">
            <TabsTrigger value="children">Children</TabsTrigger>
            <TabsTrigger value="family">Family Members</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Children Tab */}
          <TabsContent value="children" className="space-y-6">
            {isLoadingChildren ? (
              <div className="text-center py-8">Loading children...</div>
            ) : localChildren && localChildren.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {localChildren.map((child: any) => (
                  <Card key={child.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full overflow-hidden bg-neutral-100 mr-3 flex items-center justify-center">
                            {child.avatarUrl ? (
                              <img 
                                src={child.avatarUrl} 
                                alt={child.name} 
                                className="h-full w-full object-cover"
                                onError={(e) => {
                                  // Hide broken image and show placeholder
                                  e.currentTarget.style.display = 'none';
                                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                                }}
                              />
                            ) : null}
                            {(!child.avatarUrl || child.avatarUrl === null) && (
                              <span className="text-xl">👤</span>
                            )}
                          </div>
                          <CardTitle className="text-xl font-semibold">{child.name}</CardTitle>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditProfile(child)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleManageDocuments(child.id)}>
                              <FileText className="h-4 w-4 mr-2" />
                              Manage Documents
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => setChildToDelete({ id: child.id, name: child.name })}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      {(child.birthMonth || child.birthYear) && (
                        <CardDescription>
                          Born: {child.birthMonth || ''} {child.birthYear || ''}
                        </CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="mt-2 space-y-4">
                        <div className="flex justify-between items-center text-sm text-neutral-600">
                          <span>Documents</span>
                          <span>{child.documentCount || 0}</span>
                        </div>
                        <div className="flex justify-between items-center text-sm text-neutral-600">
                          <span>Last updated</span>
                          <span>{child.updatedAt ? new Date(child.updatedAt).toLocaleDateString() : 'Never'}</span>
                        </div>
                        
                        <div className="pt-4 grid grid-cols-2 gap-2">
                          <Link 
                            to={`/document-upload/${child.id}`}
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          >
                            <Button 
                              variant="outline" 
                              className="w-full flex items-center justify-center"
                            >
                              <Upload className="h-4 w-4 mr-1" />
                              Upload IEP
                            </Button>
                          </Link>
                          
                          <Button 
                            variant="default" 
                            className="w-full bg-primary hover:bg-primary-dark flex items-center justify-center transition-colors"
                            asChild
                          >
                            <Link href={`/child-profile/${child.id}`}>
                              <User className="h-4 w-4 mr-1" />
                              Profile
                            </Link>
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                          <Button 
                            variant="secondary" 
                            className="w-full flex items-center justify-center"
                            disabled={!child.documentCount}
                            asChild={!!child.documentCount}
                          >
                            {child.documentCount ? (
                              <Link href={`/document-viewer?childId=${child.id}`}>
                                <FileText className="h-4 w-4 mr-1" />
                                Documents
                              </Link>
                            ) : (
                              <span>
                                <FileText className="h-4 w-4 mr-1" />
                                Documents
                              </span>
                            )}
                          </Button>
                          
                          <Button 
                            variant="secondary" 
                            className="w-full flex items-center justify-center text-primary hover:bg-primary-dark hover:text-white transition-colors"
                            disabled={!child.documents || child.documents.length === 0}
                            asChild={child.documents && child.documents.length > 0}
                          >
                            {child.documents && child.documents.length > 0 ? (
                              <Link href={`/conversation/${child.id}`}>
                                <MessageSquare className="h-4 w-4 mr-1" />
                                Converse
                              </Link>
                            ) : (
                              <span onClick={() => {
                                toast({
                                  title: "No documents found",
                                  description: "Please upload an IEP document first to start a conversation.",
                                  variant: "destructive"
                                });
                              }}>
                                <MessageSquare className="h-4 w-4 mr-1" />
                                Converse
                              </span>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 bg-neutral-50 rounded-lg">
                <FileText className="h-12 w-12 mx-auto text-neutral-400" />
                <h3 className="mt-4 text-lg font-medium text-neutral-900">No children added yet</h3>
                <p className="mt-1 text-neutral-600">Add your first child to get started</p>
                <Button 
                  className="mt-4 bg-primary hover:bg-primary-dark"
                  onClick={() => setIsAddChildDialogOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Child
                </Button>
              </div>
            )}
          </TabsContent>

          {/* Family Members Tab */}
          <TabsContent value="family" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Family Members</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {isLoadingFamilyMembers || isLoadingFamilyInvitations ? (
                    <div className="text-center py-4">Loading family members...</div>
                  ) : (
                    <div className="space-y-4">
                      {/* Current user */}
                      <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-md">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarImage src={user?.profileImageUrl} />
                            <AvatarFallback>
                              {user?.firstName?.charAt(0) || user?.email?.charAt(0).toUpperCase() || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user?.firstName} {user?.lastName} (You)</p>
                            <p className="text-sm text-neutral-500">{user?.email}</p>
                          </div>
                        </div>
                        <span className="text-sm font-medium bg-primary/10 text-primary px-2 py-1 rounded">Owner</span>
                      </div>

                      {/* Other family members */}
                      {familyMembers && familyMembers.length > 0 ? (
                        familyMembers.map((member: any) => (
                          <div key={member.id} className="flex items-center justify-between p-3 bg-neutral-50 rounded-md">
                            <div className="flex items-center space-x-3">
                              <Avatar>
                                <AvatarImage src={member.profileImageUrl} />
                                <AvatarFallback>
                                  {member.firstName?.charAt(0) || member.email?.charAt(0).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">{member.firstName} {member.lastName}</p>
                                <p className="text-sm text-neutral-500">{member.email}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium bg-secondary/10 text-secondary px-2 py-1 rounded">Member</span>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem 
                                    className="text-red-600"
                                    onClick={() => {
                                      if (confirm(`Are you sure you want to remove ${member.firstName} ${member.lastName} from your family workspace?`)) {
                                        // TODO: Implement remove family member
                                        toast({
                                          title: "Feature coming soon",
                                          description: "Family member removal will be available soon.",
                                          variant: "default"
                                        });
                                      }
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Remove Access
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))
                      ) : null}

                      {/* Pending Invitations */}
                      {familyInvitations && familyInvitations.filter((inv: any) => inv.status === 'pending').length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-neutral-700">Pending Invitations</h4>
                          {familyInvitations
                            .filter((invitation: any) => invitation.status === 'pending')
                            .map((invitation: any) => (
                            <div key={invitation.id} className="flex items-center justify-between p-3 bg-amber-50 border border-amber-200 rounded-md">
                              <div className="flex items-center space-x-3">
                                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                                  <Users className="h-5 w-5 text-amber-600" />
                                </div>
                                <div>
                                  <p className="font-medium text-amber-900">{invitation.email}</p>
                                  <p className="text-sm text-amber-700">
                                    Invitation sent {invitation.createdAt ? new Date(invitation.createdAt).toLocaleDateString() : ''}
                                  </p>
                                </div>
                              </div>
                              <span className="text-xs font-medium bg-amber-100 text-amber-800 px-2 py-1 rounded">
                                Pending
                              </span>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Recently Accepted Invitations */}
                      {familyInvitations && familyInvitations.filter((inv: any) => inv.status === 'accepted').length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-neutral-700">Recently Joined</h4>
                          {familyInvitations
                            .filter((invitation: any) => invitation.status === 'accepted')
                            .map((invitation: any) => (
                            <div key={invitation.id} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                              <div className="flex items-center space-x-3">
                                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                  <CheckCircle className="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                  <p className="font-medium text-green-900">{invitation.email}</p>
                                  <p className="text-sm text-green-700">
                                    Joined {invitation.respondedAt ? new Date(invitation.respondedAt).toLocaleDateString() : ''}
                                  </p>
                                </div>
                              </div>
                              <span className="text-xs font-medium bg-green-100 text-green-800 px-2 py-1 rounded">
                                Joined
                              </span>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Show "no family members" only if no members AND no pending invitations */}
                      {(!familyMembers || familyMembers.length === 0) && 
                       (!familyInvitations || familyInvitations.length === 0) && (
                        <div className="text-center py-4 bg-neutral-50 rounded-lg">
                          <Users className="h-12 w-12 mx-auto text-neutral-400" />
                          <h3 className="mt-2 text-lg font-medium text-neutral-900">No family members</h3>
                          <p className="mt-1 text-neutral-600">Invite your partner or spouse to collaborate</p>
                        </div>
                      )}

                      {/* Invite form */}
                      <form onSubmit={handleInviteFamilyMember}>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="inviteFirstName">First Name *</Label>
                            <Input 
                              id="inviteFirstName" 
                              type="text"
                              value={inviteFirstName}
                              onChange={(e) => setInviteFirstName(e.target.value)}
                              placeholder="Enter first name"
                              className="mt-1"
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor="inviteLastName">Last Name</Label>
                            <Input 
                              id="inviteLastName" 
                              type="text"
                              value={inviteLastName}
                              onChange={(e) => setInviteLastName(e.target.value)}
                              placeholder="Enter last name (optional)"
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <Label htmlFor="inviteEmail">Email Address *</Label>
                            <Input 
                              id="inviteEmail" 
                              type="email"
                              value={inviteEmail}
                              onChange={(e) => setInviteEmail(e.target.value)}
                              placeholder="Enter email address"
                              className="mt-1"
                              required
                            />
                          </div>
                          <Button 
                            type="submit" 
                            className="w-full bg-primary hover:bg-primary-dark"
                            disabled={inviteFamilyMemberMutation.isPending}
                          >
                            {inviteFamilyMemberMutation.isPending ? "Sending..." : "Send Invite"}
                          </Button>
                        </div>
                      </form>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSaveSettings} className="space-y-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input 
                      id="firstName" 
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className="mt-1"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input 
                      id="lastName" 
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className="mt-1"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input 
                      id="email" 
                      value={user?.email || ""}
                      disabled
                      className="mt-1"
                    />
                  </div>
                  <div className="pt-4">
                    <Button 
                      type="submit"
                      className="bg-primary hover:bg-primary-dark"
                      disabled={isSavingSettings}
                    >
                      {isSavingSettings ? "Saving..." : "Save Changes"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
      
      {/* Document Upload Modal */}
      <Dialog open={showUploadDocumentModal} onOpenChange={setShowUploadDocumentModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload IEP Document</DialogTitle>
          </DialogHeader>
          <form 
            onSubmit={(e) => {
              e.preventDefault();
              
              if (!activeChildId) {
                toast({
                  title: "Error",
                  description: "No child selected. Please try again.",
                  variant: "destructive"
                });
                return;
              }
              
              if (!documentFile) {
                toast({
                  title: "Missing document",
                  description: "Please select a file to upload.",
                  variant: "destructive"
                });
                return;
              }
              
              // Create form data
              const formData = new FormData();
              formData.append('document', documentFile);
              formData.append('title', documentTitle || documentFile.name);
              formData.append('documentType', documentType);
              formData.append('schoolYear', documentSchoolYear || '');
              
              // Show upload in progress notification
              toast({
                title: "Uploading document...",
                description: "Please wait while your document is being uploaded."
              });
              
              // Direct fetch instead of using mutation
              fetch(`/api/children/${activeChildId}/documents/upload`, {
                method: 'POST',
                body: formData
              })
              .then(response => {
                if (!response.ok) {
                  throw new Error('Upload failed');
                }
                return response.json();
              })
              .then(data => {
                console.log("Upload successful, response:", data);
                
                // Reset form
                setDocumentFile(null);
                setDocumentTitle("");
                setDocumentType("IEP");
                setDocumentSchoolYear("");
                setShowUploadDocumentModal(false);
                
                // Show success message
                toast({
                  title: "Document uploaded",
                  description: "Your document has been uploaded successfully."
                });
                
                // Refresh data
                queryClient.invalidateQueries({ queryKey: ['/api/children'] });
                
                // Navigate to document viewer
                if (data && data.id) {
                  navigate(`/document-viewer/${data.id}`);
                }
              })
              .catch(error => {
                console.error("Upload error:", error);
                toast({
                  title: "Upload failed",
                  description: "There was a problem uploading your document.",
                  variant: "destructive"
                });
              });
            }} 
            className="space-y-4 mt-4"
          >
            <div>
              <Label htmlFor="documentTitle">Document Title</Label>
              <Input 
                id="documentTitle" 
                value={documentTitle} 
                onChange={(e) => setDocumentTitle(e.target.value)} 
                placeholder="E.g., Spring 2024 IEP"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="documentType">Document Type</Label>
              <select 
                id="documentType"
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
              >
                <option value="IEP">IEP</option>
                <option value="Evaluation">Evaluation</option>
                <option value="Progress Report">Progress Report</option>
                <option value="Other">Other</option>
              </select>
            </div>
            
            <div>
              <Label htmlFor="documentSchoolYear">School Year</Label>
              <Input 
                id="documentSchoolYear" 
                value={documentSchoolYear} 
                onChange={(e) => setDocumentSchoolYear(e.target.value)} 
                placeholder="E.g., 2023-2024"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="documentFile">Document File</Label>
              <Input
                id="documentFile"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];
                    console.log("File selected:", file.name);
                    setDocumentFile(file);
                    
                    // Auto-fill title if empty
                    if (!documentTitle) {
                      setDocumentTitle(file.name.split('.')[0]);
                    }
                  }
                }}
                className="mt-1 cursor-pointer file:cursor-pointer file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark"
                required
              />
              <p className="text-xs text-neutral-500 mt-1">
                Upload an IEP document, evaluation, or related file (PDF, Word, or Text).
              </p>
            </div>
            
            <div className="flex space-x-2 pt-2">
              <Button 
                type="button" 
                variant="outline" 
                className="flex-1"
                onClick={() => setShowUploadDocumentModal(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1 bg-primary hover:bg-primary-dark"
              >
                Upload Document
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Document List/Management Modal */}
      <Dialog open={showDocumentListModal} onOpenChange={setShowDocumentListModal}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Manage Documents</DialogTitle>
          </DialogHeader>
          
          {activeChildId && (
            <div className="mt-4">
              {/* Document List Query */}
              {(() => {
                const { data: documents, isLoading } = useQuery({
                  queryKey: [`/api/children/${activeChildId}/documents`],
                  enabled: !!activeChildId
                });
                
                if (isLoading) {
                  return (
                    <div className="py-8 text-center">
                      Loading documents...
                    </div>
                  );
                }
                
                if (!documents || documents.length === 0) {
                  return (
                    <div className="py-8 text-center">
                      <p className="text-neutral-500 mb-4">No documents found for this child.</p>
                      <Button 
                        onClick={() => {
                          setShowDocumentListModal(false);
                          setShowUploadDocumentModal(true);
                        }}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Document
                      </Button>
                    </div>
                  );
                }
                
                return (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">Documents</h3>
                      <Button 
                        size="sm"
                        onClick={() => {
                          setShowDocumentListModal(false);
                          setShowUploadDocumentModal(true);
                        }}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload New
                      </Button>
                    </div>
                    
                    <div className="border rounded-md">
                      <div className="grid grid-cols-12 bg-neutral-50 p-3 border-b font-medium text-sm">
                        <div className="col-span-5">Title</div>
                        <div className="col-span-2">Type</div>
                        <div className="col-span-2">School Year</div>
                        <div className="col-span-2">Date Added</div>
                        <div className="col-span-1"></div>
                      </div>
                      
                      <div className="max-h-96 overflow-y-auto">
                        {documents.map((doc: any) => (
                          <div key={doc.id} className="grid grid-cols-12 p-3 border-b last:border-0 items-center text-sm hover:bg-neutral-50">
                            <div className="col-span-5 font-medium truncate">{doc.title}</div>
                            <div className="col-span-2 text-neutral-600">{doc.documentType}</div>
                            <div className="col-span-2 text-neutral-600">{doc.schoolYear || '-'}</div>
                            <div className="col-span-2 text-neutral-600">
                              {new Date(doc.createdAt).toLocaleDateString()}
                            </div>
                            <div className="col-span-1 flex justify-end">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem asChild>
                                    <Link href={`/document-viewer/${doc.id}`}>
                                      View
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem asChild>
                                    <Link href={`/conversation/${activeChildId}`}>
                                      Discuss with AI
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    className="text-red-600"
                                    onClick={() => {
                                      if (confirm("Are you sure you want to delete this document? This action cannot be undone.")) {
                                        deleteDocumentMutation.mutate(doc.id);
                                      }
                                    }}
                                  >
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Add the DeleteChildDialog */}
      <DeleteChildDialog
        isOpen={!!childToDelete}
        onClose={() => setChildToDelete(null)}
        onConfirm={() => {
          if (childToDelete) {
            deleteChildMutation.mutate(childToDelete.id);
          }
        }}
        childName={childToDelete?.name || ""}
      />
      
      <FeedbackWidget page="Dashboard" />
      <Footer />
    </>
  );
}