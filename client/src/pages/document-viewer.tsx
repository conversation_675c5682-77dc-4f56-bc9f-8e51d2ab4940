import React, { useState, useEffect } from 'react';
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link, useLocation, useParams } from "wouter";
import { 
  ChevronLeft, ChevronRight, Download, 
  MessageSquare, FileText, ExternalLink, Upload,
  MoreHorizontal, Trash2, Edit
} from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Tit<PERSON>,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getDocumentSummary, analyzeIEPGoals, getSuggestedQuestions } from "@/lib/openai";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import FeedbackWidget from "@/components/FeedbackWidget";
import ReactMarkdown from 'react-markdown';

export default function DocumentViewer() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const params = useParams();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  
  // Get document ID from URL params or query parameters
  const urlParams = new URLSearchParams(window.location.search);
  const documentId = params.id || urlParams.get('id') || "";
  const childId = urlParams.get('childId') || "";
  
  console.log("Document viewer - Document ID:", documentId);
  console.log("Document viewer - Child ID:", childId);
  
  // Determine view mode: list view (childId only) or document view (documentId)
  const isListView = !documentId && childId;
  const isDocumentView = !!documentId;
  
  // Redirect to dashboard if neither ID is provided
  useEffect(() => {
    if (!documentId && !childId && !isLoading) {
      console.log("No document ID or child ID found, redirecting to dashboard");
      navigate('/dashboard');
    }
  }, [documentId, childId, isLoading, navigate]);
  
  const [activeTab, setActiveTab] = useState("document");
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  
  // Edit document modal states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingDocument, setEditingDocument] = useState<any>(null);
  const [editTitle, setEditTitle] = useState("");
  const [editDocumentType, setEditDocumentType] = useState("");
  const [editSchoolYear, setEditSchoolYear] = useState("");
  
  // Redirect to home if not authenticated
  if (!isLoading && !isAuthenticated) {
    navigate('/');
    return null;
  }

  // Query for document information (only when viewing specific document)
  const { data: document, isLoading: isLoadingDocument } = useQuery({
    queryKey: [`/api/documents/${documentId}`],
    enabled: Boolean(isAuthenticated && documentId && isDocumentView),
  });

  // Query for child information and documents list (for list view)
  const { data: childDocuments, isLoading: isLoadingChildDocuments } = useQuery({
    queryKey: [`/api/children/${childId}/documents`],
    enabled: Boolean(isAuthenticated && childId && isListView),
  });

  // Query for child information (to get child name for list view)
  const { data: childInfo, isLoading: isLoadingChildInfo } = useQuery({
    queryKey: [`/api/children/${childId}`],
    enabled: Boolean(isAuthenticated && childId && isListView),
  });

  // Query for document summary (only for document view)
  const { 
    data: summary, 
    isLoading: isLoadingSummary,
    refetch: refetchSummary
  } = useQuery({
    queryKey: [`/api/ai/summary/${documentId}`],
    enabled: Boolean(isAuthenticated && documentId && isDocumentView && activeTab === "insights"),
    queryFn: () => getDocumentSummary(documentId),
  });

  // Query for document goals (only for document view)
  const { 
    data: goals, 
    isLoading: isLoadingGoals,
    refetch: refetchGoals
  } = useQuery({
    queryKey: [`/api/ai/goals/${documentId}`],
    enabled: Boolean(isAuthenticated && documentId && isDocumentView && activeTab === "insights"),
    queryFn: () => analyzeIEPGoals(documentId),
  });

  // Query for suggested questions (only for document view)
  const { 
    data: suggestedQuestions, 
    isLoading: isLoadingSuggestions,
    refetch: refetchSuggestions
  } = useQuery({
    queryKey: [`/api/ai/suggested-questions/${documentId}`],
    enabled: Boolean(isAuthenticated && documentId && isDocumentView && activeTab === "insights"),
    queryFn: () => getSuggestedQuestions(documentId),
  });

  // Mutation to delete a document
  const deleteDocumentMutation = useMutation({
    mutationFn: async (docId: string) => {
      const response = await apiRequest('DELETE', `/api/documents/${docId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/children/${childId}/documents`] });
      toast({
        title: "Document deleted",
        description: "The document has been permanently deleted."
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to delete document",
        description: "There was a problem deleting the document. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Mutation to update a document
  const updateDocumentMutation = useMutation({
    mutationFn: async ({ docId, updates }: { docId: string; updates: any }) => {
      const response = await apiRequest('PUT', `/api/documents/${docId}`, updates);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/children/${childId}/documents`] });
      setShowEditModal(false);
      setEditingDocument(null);
      toast({
        title: "Document updated",
        description: "The document has been updated successfully."
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update document",
        description: "There was a problem updating the document. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Load PDF for viewing (only for document view)
  useEffect(() => {
    if (document?.fileUrl && isDocumentView) {
      console.log("Document fileUrl:", document.fileUrl);
      setPdfUrl(document.fileUrl);
    }
  }, [document, isDocumentView]);

  // Handle tab change (only for document view)
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === "insights" && isDocumentView) {
      refetchSummary();
      refetchGoals();
      refetchSuggestions();
    }
  };

  // Handle delete document
  const handleDeleteDocument = (docId: string, docTitle: string) => {
    if (confirm(`Are you sure you want to delete "${docTitle}"? This action cannot be undone.`)) {
      deleteDocumentMutation.mutate(docId);
    }
  };

  // Handle edit document
  const handleEditDocument = (doc: any) => {
    setEditingDocument(doc);
    setEditTitle(doc.title);
    setEditDocumentType(doc.documentType);
    setEditSchoolYear(doc.schoolYear || "");
    setShowEditModal(true);
  };

  // Handle save document changes
  const handleSaveDocumentChanges = () => {
    if (!editingDocument) return;
    
    updateDocumentMutation.mutate({
      docId: editingDocument.id,
      updates: {
        title: editTitle,
        documentType: editDocumentType,
        schoolYear: editSchoolYear
      }
    });
  };

  // LIST VIEW RENDER
  if (isListView) {
    return (
      <>
        <Navbar />
        <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
          {isLoadingChildInfo || isLoadingChildDocuments ? (
            <div className="text-center py-8">Loading documents...</div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-8">
                <div>
                  <div className="flex items-center text-sm text-neutral-600 mb-2">
                    <Link href="/dashboard" className="text-primary hover:text-primary-dark">
                      Dashboard
                    </Link>
                    <ChevronRight className="h-4 w-4 mx-2" />
                    <Link href={`/document-viewer?childId=${childId}`} className="text-primary hover:text-primary-dark">
                      {childInfo?.name || 'Child'} Documents
                    </Link>
                  </div>
                  <h1 className="text-3xl font-bold text-neutral-900 mt-2">
                    Documents for {childInfo?.name || 'Child'}
                  </h1>
                  <div className="flex items-center mt-1 text-neutral-600">
                    <span>{childDocuments?.length || 0} documents</span>
                  </div>
                </div>
                <div className="flex space-x-4">
                  <Button 
                    className="bg-primary hover:bg-primary-dark"
                    asChild
                  >
                    <Link href={`/document-upload/${childId}`}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Document
                    </Link>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="text-primary border-primary hover:bg-primary-light/10"
                    disabled={!childDocuments || childDocuments.length === 0}
                    asChild={childDocuments && childDocuments.length > 0}
                  >
                    {childDocuments && childDocuments.length > 0 ? (
                      <Link href={`/conversation/${childId}`}>
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Ask Questions
                      </Link>
                    ) : (
                      <span>
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Ask Questions
                      </span>
                    )}
                  </Button>
                </div>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Documents</CardTitle>
                </CardHeader>
                <CardContent>
                  {!childDocuments || childDocuments.length === 0 ? (
                    <div className="text-center py-12 bg-neutral-50 rounded-lg">
                      <FileText className="h-12 w-12 mx-auto text-neutral-400" />
                      <h3 className="mt-4 text-lg font-medium text-neutral-900">No documents found</h3>
                      <p className="mt-1 text-neutral-600">Upload your first IEP document to get started</p>
                      <Button 
                        className="mt-4 bg-primary hover:bg-primary-dark"
                        asChild
                      >
                        <Link href={`/document-upload/${childId}`}>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Document
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="border rounded-md">
                      <div className="grid grid-cols-12 bg-neutral-50 p-3 border-b font-medium text-sm">
                        <div className="col-span-4">Title</div>
                        <div className="col-span-2">Type</div>
                        <div className="col-span-2">School Year</div>
                        <div className="col-span-3">Date Added</div>
                        <div className="col-span-1"></div>
                      </div>
                      
                      <div className="divide-y">
                        {childDocuments.map((doc: any) => (
                          <div key={doc.id} className="grid grid-cols-12 p-3 items-center text-sm hover:bg-neutral-50">
                            <div className="col-span-4">
                              <Link href={`/document-viewer/${doc.id}`}>
                                <a className="font-medium text-primary hover:text-primary-dark truncate block">
                                  {doc.title}
                                </a>
                              </Link>
                            </div>
                            <div className="col-span-2 text-neutral-600">{doc.documentType}</div>
                            <div className="col-span-2 text-neutral-600">{doc.schoolYear || '-'}</div>
                            <div className="col-span-3 text-neutral-600">
                              {new Date(doc.createdAt).toLocaleDateString()}
                            </div>
                            <div className="col-span-1 flex justify-end">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem asChild>
                                    <Link href={`/document-viewer/${doc.id}`}>
                                      <FileText className="h-4 w-4 mr-2" />
                                      View Document
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => handleEditDocument(doc)}
                                  >
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => window.open(doc.fileUrl, '_blank')}
                                  >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    className="text-red-600"
                                    onClick={() => handleDeleteDocument(doc.id, doc.title)}
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </main>
        
        {/* Edit Document Modal */}
        <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Document Details</DialogTitle>
            </DialogHeader>
            <form 
              onSubmit={(e) => {
                e.preventDefault();
                handleSaveDocumentChanges();
              }} 
              className="space-y-4 mt-4"
            >
              <div>
                <Label htmlFor="editTitle">Document Title</Label>
                <Input 
                  id="editTitle" 
                  value={editTitle} 
                  onChange={(e) => setEditTitle(e.target.value)} 
                  placeholder="Document title"
                  className="mt-1"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="editDocumentType">Document Type</Label>
                <select 
                  id="editDocumentType"
                  value={editDocumentType}
                  onChange={(e) => setEditDocumentType(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                  required
                >
                  <option value="IEP">IEP</option>
                  <option value="Evaluation">Evaluation</option>
                  <option value="Progress Report">Progress Report</option>
                  <option value="504 Plan">504 Plan</option>
                  <option value="Assessment">Assessment</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div>
                <Label htmlFor="editSchoolYear">School Year</Label>
                <Input 
                  id="editSchoolYear" 
                  value={editSchoolYear} 
                  onChange={(e) => setEditSchoolYear(e.target.value)} 
                  placeholder="e.g., 2023-2024"
                  className="mt-1"
                />
              </div>
              
              <div className="flex space-x-2 pt-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setShowEditModal(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  className="flex-1 bg-primary hover:bg-primary-dark"
                  disabled={updateDocumentMutation.isPending}
                >
                  {updateDocumentMutation.isPending ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
        
        <Footer />
      </>
    );
  }

  // DOCUMENT VIEW RENDER (existing functionality)
  return (
    <>
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {isLoadingDocument ? (
          <div className="text-center py-8">Loading document...</div>
        ) : document ? (
          <>
            <div className="flex justify-between items-center mb-8">
              <div>
                <div className="flex items-center text-sm text-neutral-600 mb-2">
                  <Link href="/dashboard" className="text-primary hover:text-primary-dark">
                    Dashboard
                  </Link>
                  <ChevronRight className="h-4 w-4 mx-2" />
                  <Link href={`/document-viewer?childId=${document.childId}`} className="text-primary hover:text-primary-dark">
                    {document.childName} Documents
                  </Link>
                  <ChevronRight className="h-4 w-4 mx-2" />
                  <span className="text-neutral-800">{document.title}</span>
                </div>
                <h1 className="text-3xl font-bold text-neutral-900 mt-2">{document.title}</h1>
                <div className="flex items-center mt-1 text-neutral-600">
                  <span>{document.documentType}</span>
                  <span className="mx-2">•</span>
                  <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
              <div className="flex space-x-4">
                <Button 
                  variant="outline"
                  className="hover:bg-neutral-100"
                  onClick={() => window.open(document.fileUrl, '_blank')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
              <TabsList className="grid w-full grid-cols-2 max-w-md">
                <TabsTrigger value="document">Document</TabsTrigger>
                <TabsTrigger value="insights">AI Insights</TabsTrigger>
              </TabsList>

              {/* Document Viewer Tab */}
              <TabsContent value="document">
                <Card className="min-h-[800px]">
                  <CardContent className="p-0 h-full">
                    {pdfUrl ? (
                      <iframe 
                        src={`${pdfUrl}#toolbar=0`}
                        className="w-full h-[800px] border-0" 
                        title={document.title}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-[800px] bg-neutral-50">
                        <div className="text-center">
                          <FileText className="h-12 w-12 mx-auto text-neutral-400" />
                          <h3 className="mt-4 text-lg font-medium text-neutral-900">Document preview not available</h3>
                          <p className="mt-1 text-neutral-600">Try downloading the document to view it</p>
                          <Button 
                            className="mt-4"
                            variant="outline"
                            onClick={() => window.open(document.fileUrl, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Open Document
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* AI Insights Tab */}
              <TabsContent value="insights">
                <div className="mb-6 p-4 bg-yellow-100 border-l-4 border-yellow-400 text-yellow-800 rounded">
                  <strong>Warning:</strong> IEPs.ai can make mistakes. Please double-check responses.
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Document Summary */}
                  <Card className="md:col-span-2">
                    <CardHeader>
                      <CardTitle>Document Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingSummary ? (
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-5/6" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-3/4" />
                        </div>
                      ) : summary ? (
                        <div className="prose prose-sm max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-li:text-gray-700 prose-strong:text-gray-900 prose-headings:mb-3 prose-p:mb-3 prose-li:mb-1 prose-ol:mb-4 prose-ul:mb-4">
                          <ReactMarkdown>{summary.answer}</ReactMarkdown>
                        </div>
                      ) : (
                        <p className="text-neutral-600">Unable to generate summary. Try refreshing the page.</p>
                      )}
                    </CardContent>
                  </Card>

                  {/* Suggested Questions */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Suggested Questions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingSuggestions ? (
                        <div className="space-y-2">
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                        </div>
                      ) : suggestedQuestions && suggestedQuestions.length > 0 ? (
                        <div className="space-y-2">
                          {suggestedQuestions.map((question, index) => (
                            <div 
                              key={index}
                              className="w-full justify-start text-left h-auto py-2 px-3 border rounded-md bg-neutral-50 text-sm cursor-default"
                            >
                              {question}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-neutral-600">No suggested questions available.</p>
                      )}
                    </CardContent>
                  </Card>

                  {/* IEP Goals Analysis */}
                  <Card className="md:col-span-3">
                    <CardHeader>
                      <CardTitle>IEP Goals Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingGoals ? (
                        <div className="space-y-4">
                          <Skeleton className="h-16 w-full" />
                          <Skeleton className="h-16 w-full" />
                          <Skeleton className="h-16 w-full" />
                        </div>
                      ) : goals && goals.goals ? (
                        <div className="space-y-4">
                          {goals.goals.map((goal: any, index: number) => (
                            <div key={index} className="border rounded-md p-4 bg-neutral-50">
                              <h3 className="font-medium text-neutral-900">{goal.area}</h3>
                              <p className="mt-1 text-neutral-700">{goal.description}</p>
                              {goal.measurableOutcomes && (
                                <div className="mt-2">
                                  <span className="text-sm font-medium text-neutral-600">Measurable Outcomes:</span>
                                  <p className="text-sm text-neutral-600">{goal.measurableOutcomes}</p>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-neutral-600">No goal analysis available for this document.</p>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <div className="text-center py-8">
            <h2 className="text-xl font-medium text-neutral-900">Document not found</h2>
            <p className="mt-2 text-neutral-600">The requested document does not exist or you don't have access.</p>
            <Button 
              className="mt-4 bg-primary hover:bg-primary-dark"
              asChild
            >
              <Link href="/dashboard">
                Back to Dashboard
              </Link>
            </Button>
          </div>
        )}
      </main>
      
      <FeedbackWidget page="Document Viewer" />
      <Footer />
    </>
  );
}