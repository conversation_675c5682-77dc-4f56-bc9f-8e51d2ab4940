import { useState } from "react";
import { Calendar, CheckCircle, Clock, Target, Users, Zap, FileText, MessageSquare, Settings, Star } from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import FeedbackWidget from "@/components/FeedbackWidget";

interface RoadmapItem {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'in-progress' | 'planned' | 'future';
  quarter: string;
  priority: 'high' | 'medium' | 'low';
  category: 'core' | 'ai' | 'collaboration' | 'analytics';
  icon: any;
}

const roadmapData: RoadmapItem[] = [
  {
    id: '1',
    title: 'Streaming responses',
    description: 'Enhance conversation responses with streaming technology for user experience.',
    status: 'completed',
    quarter: 'Q2 2025',
    priority: 'medium',
    category: 'core',
    icon: FileText
  },
  {
    id: '2',
    title: 'Enhanced Document Upload (S3)',
    description: 'Improved file upload system with support for larger files and better error handling',
    status: 'completed',
    quarter: 'Q2 2025',
    priority: 'high',
    category: 'core',
    icon: FileText
  },
  {
    id: '3',
    title: 'Document Viewing & Downloading',
    description: 'View, edit, and download documents directly from the platform',
    status: 'completed',
    quarter: 'Q2 2025',
    priority: 'high',
    category: 'core',
    icon: FileText
  },
  {
    id: '4',
    title: 'Lambda Refactor',
    description: 'Ability to extract text from scanned documents',
    status: 'completed',
    quarter: 'Q2 2025',
    priority: 'high',
    category: 'core',
    icon: FileText
  },
  {
    id: '5',
    title: 'AI-Powered Document Analysis',
    description: 'Advanced AI assistant that can analyze IEP documents and provide insights',
    status: 'completed',
    quarter: 'Q2 2025',
    priority: 'high',
    category: 'ai',
    icon: Zap
  },
  {
    id: '6',
    title: 'Real-time Family Collaboration',
    description: 'Enable multiple family members to collaborate on IEP management in real-time',
    status: 'completed',
    quarter: 'Q2 2025',
    priority: 'medium',
    category: 'collaboration',
    icon: Users
  },
  {
    id: '7',
    title: 'Goal Tracking Dashboard',
    description: 'Visual dashboard to track IEP goals and milestones over time',
    status: 'in-progress',
    quarter: 'Q3 2025',
    priority: 'high',
    category: 'analytics',
    icon: Target
  },
  {
    id: '8',
    title: 'Vectorized Semantic Search',
    description: 'Including Individuals with Disabilities Act (IDEA), Section 504, and other relevant laws in our models',
    status: 'in-progress',
    quarter: 'Q3 2025',
    priority: 'medium',
    category: 'core',
    icon: Settings
  },
  {
    id: '9',
    title: 'Advanced Conversation Templates',
    description: 'Pre-built conversation templates for common IEP scenarios and questions',
    status: 'planned',
    quarter: 'Q4 2025',
    priority: 'low',
    category: 'ai',
    icon: MessageSquare
  },
  {
    id: '10',
    title: 'Progress Analytics',
    description: 'Detailed analytics and reporting on child progress and IEP effectiveness',
    status: 'future',
    quarter: 'Q4 2025',
    priority: 'high',
    category: 'analytics',
    icon: Star
  },
  {
    id: '11',
    title: 'Integration with School Systems',
    description: 'Direct integration with popular school management systems and platforms',
    status: 'future',
    quarter: 'Q1 2026',
    priority: 'high',
    category: 'core',
    icon: Settings
  }
];

const statusConfig = {
  completed: { label: 'Completed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'in-progress': { label: 'In Progress', color: 'bg-blue-100 text-blue-800', icon: Clock },
  planned: { label: 'Planned', color: 'bg-yellow-100 text-yellow-800', icon: Calendar },
  future: { label: 'Future', color: 'bg-gray-100 text-gray-800', icon: Target }
};

const priorityConfig = {
  high: { label: 'High', color: 'bg-red-100 text-red-800' },
  medium: { label: 'Medium', color: 'bg-orange-100 text-orange-800' },
  low: { label: 'Low', color: 'bg-green-100 text-green-800' }
};

const categoryConfig = {
  core: { label: 'Core Platform', color: 'bg-purple-100 text-purple-800' },
  ai: { label: 'AI Features', color: 'bg-indigo-100 text-indigo-800' },
  collaboration: { label: 'Collaboration', color: 'bg-pink-100 text-pink-800' },
  analytics: { label: 'Analytics', color: 'bg-cyan-100 text-cyan-800' }
};

export default function RoadmapPage() {
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredItems = roadmapData.filter(item => {
    if (selectedStatus !== 'all' && item.status !== selectedStatus) return false;
    if (selectedCategory !== 'all' && item.category !== selectedCategory) return false;
    return true;
  });

  const groupedByQuarter = filteredItems.reduce((acc, item) => {
    if (!acc[item.quarter]) acc[item.quarter] = [];
    acc[item.quarter].push(item);
    return acc;
  }, {} as Record<string, RoadmapItem[]>);

  return (
    <>
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">Product Roadmap</h1>
          <p className="text-neutral-600 text-lg">
            Our vision for the future of IEP management and family collaboration
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium text-neutral-700 py-2">Status:</span>
                <Button
                  variant={selectedStatus === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('all')}
                >
                  All
                </Button>
                {Object.entries(statusConfig).map(([status, config]) => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {config.label}
                  </Button>
                ))}
              </div>
              
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium text-neutral-700 py-2">Category:</span>
                <Button
                  variant={selectedCategory === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('all')}
                >
                  All
                </Button>
                {Object.entries(categoryConfig).map(([category, config]) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {config.label}
                  </Button>
                ))}
              </div>
            </div>
          </Tabs>
        </div>

        {/* Roadmap Timeline */}
        <div className="space-y-8">
          {Object.entries(groupedByQuarter)
            .sort(([a], [b]) => {
              // Extract quarter and year from strings like "Q1 2025", "Q3 2026"
              const [quarterA, yearA] = a.split(' ');
              const [quarterB, yearB] = b.split(' ');
              
              // First sort by year
              if (yearA !== yearB) {
                return parseInt(yearA) - parseInt(yearB);
              }
              
              // If same year, sort by quarter
              const qNumA = parseInt(quarterA.replace('Q', ''));
              const qNumB = parseInt(quarterB.replace('Q', ''));
              return qNumA - qNumB;
            })
            .map(([quarter, items]) => (
              <div key={quarter} className="relative">
                <div className="sticky top-4 z-10 mb-4">
                  <div className="bg-white border border-neutral-200 rounded-lg px-4 py-2 shadow-sm inline-block">
                    <h2 className="text-xl font-semibold text-neutral-900">{quarter}</h2>
                  </div>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {items.map((item) => {
                    const Icon = item.icon;
                    const statusInfo = statusConfig[item.status];
                    const priorityInfo = priorityConfig[item.priority];
                    const categoryInfo = categoryConfig[item.category];
                    const StatusIcon = statusInfo.icon;
                    
                    return (
                      <Card key={item.id} className="hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="p-2 bg-primary/10 rounded-lg">
                                <Icon className="h-4 w-4 text-primary" />
                              </div>
                              <div className="flex items-center space-x-1">
                                <StatusIcon className="h-3 w-3 text-neutral-500" />
                              </div>
                            </div>
                          </div>
                          <CardTitle className="text-lg">{item.title}</CardTitle>
                        </CardHeader>
                        
                        <CardContent className="space-y-4">
                          <p className="text-neutral-600 text-sm">{item.description}</p>
                          
                          <div className="flex flex-wrap gap-2">
                            <Badge className={`${statusInfo.color} text-xs`}>
                              {statusInfo.label}
                            </Badge>
                            <Badge className={`${priorityInfo.color} text-xs`}>
                              {priorityInfo.label}
                            </Badge>
                            <Badge className={`${categoryInfo.color} text-xs`}>
                              {categoryInfo.label}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            ))}
        </div>

        {/* No Results */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Target className="h-12 w-12 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-neutral-900 mb-2">No items found</h3>
            <p className="text-neutral-600">Try adjusting your filters to see more roadmap items.</p>
          </div>
        )}

        {/* Footer Note */}
        <div className="mt-12 p-6 bg-neutral-50 rounded-lg">
          <h3 className="text-lg font-medium text-neutral-900 mb-2">About Our Roadmap</h3>
          <p className="text-neutral-600 text-sm">
            This roadmap represents our current planning and priorities. Features and timelines may change 
            based on user feedback, technical considerations, and business priorities. We're committed to 
            transparency and will update this roadmap regularly to reflect our progress and any changes 
            to our plans.
          </p>
        </div>
      </main>
      
      <FeedbackWidget page="Roadmap" />
      <Footer />
    </>
  );
}