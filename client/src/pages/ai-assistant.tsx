import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link, useLocation, useParams } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { askAIAssistant, getSuggestedQuestions } from "@/lib/openai";
import { 
  ChevronRight, SendHorizontal, 
  FileText, ArrowLeft, LightbulbIcon, 
  History
} from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ReactMarkdown from 'react-markdown';

export default function AIAssistant() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const params = useParams();
  const [location, navigate] = useLocation();
  const childId = params.childId;
  const queryParams = new URLSearchParams(location.split('?')[1] || '');
  const conversationId = queryParams.get('conversation');
  const documentId = queryParams.get('document');
  const initialQuestion = queryParams.get('question');

  const [messages, setMessages] = useState<Array<{role: string, content: string, timestamp: Date}>>([]);
  const [inputMessage, setInputMessage] = useState(initialQuestion || '');
  const [selectedDocument, setSelectedDocument] = useState<string>(documentId || '');
  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Redirect to home if not authenticated
  if (!isLoading && !isAuthenticated) {
    navigate('/');
    return null;
  }

  // Query for child information
  const { data: child, isLoading: isLoadingChild } = useQuery({
    queryKey: [`/api/children/${childId}`],
    enabled: isAuthenticated && !!childId,
  });

  // Query for child's documents
  const { data: documents, isLoading: isLoadingDocuments } = useQuery({
    queryKey: [`/api/children/${childId}/documents`],
    enabled: isAuthenticated && !!childId,
  });

  // Query for conversation history if conversationId is provided
  const { data: conversation, isLoading: isLoadingConversation } = useQuery({
    queryKey: [`/api/conversations/${conversationId}`],
    enabled: isAuthenticated && !!conversationId,
  });

  // Query for all conversations for the child
  const { data: conversations, isLoading: isLoadingConversations } = useQuery({
    queryKey: [`/api/children/${childId}/conversations`],
    enabled: isAuthenticated && !!childId,
  });

  // Query for suggested questions
  const { data: suggestedQuestions, isLoading: isLoadingSuggestions } = useQuery({
    queryKey: [`/api/ai/suggested-questions/${selectedDocument || documentId}`],
    enabled: isAuthenticated && !!(selectedDocument || documentId),
    queryFn: () => getSuggestedQuestions(selectedDocument || documentId || ''),
  });

  // Load conversation if conversationId is provided
  useEffect(() => {
    if (conversation && conversation.messages) {
      setMessages(conversation.messages.map((message: any) => ({
        role: message.role,
        content: message.content,
        timestamp: new Date(message.timestamp)
      })));
    } else if (initialQuestion) {
      // If there's an initial question in the URL, set it as the first user message
      setMessages([]);
    } else {
      // Default welcome message when starting a new conversation
      setMessages([
        {
          role: 'assistant',
          content: `Hi there! I'm your IEP AI assistant. I can help answer questions about ${child?.name}'s IEP documents. How can I assist you today?`,
          timestamp: new Date()
        }
      ]);
    }
  }, [conversation, child, initialQuestion]);

  // Scroll to bottom of messages when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({ question, documentId }: { question: string, documentId?: string }) => {
      const response = await apiRequest(
        "POST", 
        `/api/ai/ask/${childId}`, 
        { 
          question,
          documentId: documentId || selectedDocument || undefined,
          conversationId
        }
      );
      return response.json();
    },
    onSuccess: (data) => {
      setMessages(prev => [
        ...prev, 
        {
          role: 'assistant',
          content: data.answer,
          timestamp: new Date()
        }
      ]);
      // If this is a new conversation, redirect to include the new conversation ID
      if (data.conversationId && !conversationId) {
        navigate(`/assistant/${childId}?conversation=${data.conversationId}${documentId ? `&document=${documentId}` : ''}`);
      }
    },
  });

  // Create a new conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest(
        "POST", 
        `/api/children/${childId}/conversations`, 
        { title: "New Conversation" }
      );
      return response.json();
    },
    onSuccess: (data) => {
      navigate(`/assistant/${childId}?conversation=${data.id}`);
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim()) return;

    // Add user message to state immediately
    setMessages(prev => [
      ...prev, 
      {
        role: 'user',
        content: inputMessage,
        timestamp: new Date()
      }
    ]);

    // Send message to API
    sendMessageMutation.mutate({ 
      question: inputMessage,
      documentId: selectedDocument || documentId || undefined
    });

    // Clear input
    setInputMessage('');
  };

  // Handle document selection change
  const handleDocumentChange = (value: string) => {
    setSelectedDocument(value);
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <>
      <Navbar />
      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {isLoadingChild ? (
          <div className="text-center py-8">Loading...</div>
        ) : child ? (
          <>
            <div className="flex justify-between items-center mb-4">
              <div>
                <div className="flex items-center text-sm text-neutral-600 mb-2">
                  <Link href="/dashboard" className="text-primary hover:text-primary-dark">
                    Dashboard
                  </Link>
                  <ChevronRight className="h-4 w-4 mx-2" />
                  <Link href={`/child-profile/${childId}`} className="text-primary hover:text-primary-dark">
                    {child.name}'s Profile
                  </Link>
                  <ChevronRight className="h-4 w-4 mx-2" />
                  <span className="text-neutral-800">AI Assistant</span>
                </div>
                <h1 className="text-2xl font-bold text-neutral-900 mt-1">
                  AI Assistant
                  {conversation?.title && (
                    <span className="ml-2 text-neutral-500 text-lg font-normal">
                      - {conversation.title}
                    </span>
                  )}
                </h1>
              </div>
              <div className="flex space-x-2">
                <Dialog open={isHistoryDialogOpen} onOpenChange={setIsHistoryDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="flex items-center gap-2">
                      <History className="h-4 w-4" />
                      <span className="hidden sm:inline">Conversation History</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Conversation History</DialogTitle>
                    </DialogHeader>
                    <div className="mt-4 space-y-2 max-h-[60vh] overflow-y-auto pr-2">
                      {isLoadingConversations ? (
                        <>
                          <Skeleton className="h-14 w-full rounded-md" />
                          <Skeleton className="h-14 w-full rounded-md" />
                          <Skeleton className="h-14 w-full rounded-md" />
                        </>
                      ) : conversations && conversations.length > 0 ? (
                        conversations.map((conv: any) => (
                          <div 
                            key={conv.id} 
                            className={`p-3 border rounded-md hover:bg-neutral-50 cursor-pointer transition-colors ${conv.id === conversationId ? 'bg-primary/10 border-primary' : ''}`}
                            onClick={() => {
                              navigate(`/assistant/${childId}?conversation=${conv.id}`);
                              setIsHistoryDialogOpen(false);
                            }}
                          >
                            <div className="flex justify-between items-center">
                              <h3 className="font-medium">{conv.title || "Untitled Conversation"}</h3>
                              <span className="text-xs text-neutral-500">{new Date(conv.createdAt).toLocaleDateString()}</span>
                            </div>
                            <p className="text-sm text-neutral-600 truncate mt-1">
                              {conv.previewText || "No preview available"}
                            </p>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-neutral-600">No previous conversations</p>
                        </div>
                      )}
                    </div>
                    <div className="mt-4">
                      <Button 
                        className="w-full bg-primary hover:bg-primary-dark"
                        onClick={() => {
                          createConversationMutation.mutate();
                          setIsHistoryDialogOpen(false);
                        }}
                        disabled={createConversationMutation.isPending}
                      >
                        Start New Conversation
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                <Select 
                  value={selectedDocument || documentId || ''} 
                  onValueChange={handleDocumentChange}
                >
                  <SelectTrigger className="w-[180px] md:w-[250px]">
                    <SelectValue placeholder="Select document" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Documents</SelectItem>
                    {!isLoadingDocuments && documents && documents.map((doc: any) => (
                      <SelectItem key={doc.id} value={doc.id}>
                        {doc.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Main chat area */}
              <div className="md:col-span-3">
                <Card className="h-[calc(100vh-250px)]">
                  <CardContent className="p-0 flex flex-col h-full">
                    {/* Messages area */}
                    <ScrollArea className="flex-1 p-4">
                      <div className="space-y-4">
                        {messages.map((message, index) => (
                          <div 
                            key={index} 
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div 
                              className={`flex max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}
                            >
                              <div className={`flex-shrink-0 ${message.role === 'user' ? 'ml-3' : 'mr-3'}`}>
                                {message.role === 'assistant' ? (
                                  <Avatar>
                                    <AvatarFallback className="bg-primary text-white">AI</AvatarFallback>
                                  </Avatar>
                                ) : (
                                  <Avatar>
                                    <AvatarImage src={user?.profileImageUrl} alt={user?.firstName} />
                                    <AvatarFallback>
                                      {user?.firstName?.charAt(0) || user?.email?.charAt(0).toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                )}
                              </div>
                              <div>
                                <div 
                                  className={`px-4 py-3 rounded-lg ${
                                    message.role === 'user' 
                                      ? 'bg-primary text-white' 
                                      : 'bg-neutral-100 text-neutral-800'
                                  }`}
                                >
                                  {message.role === 'assistant' ? (
                                    <ReactMarkdown 
                                      className="prose prose-sm max-w-none"
                                      components={{
                                        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                                        strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
                                        em: ({ children }) => <em className="italic">{children}</em>,
                                        ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                                        ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                                        li: ({ children }) => <li className="mb-1">{children}</li>,
                                        h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                                        h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
                                        h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
                                      }}
                                    >
                                      {message.content}
                                    </ReactMarkdown>
                                  ) : (
                                    message.content
                                  )}
                                </div>
                                <div 
                                  className={`text-xs text-neutral-500 mt-1 ${
                                    message.role === 'user' ? 'text-right' : 'text-left'
                                  }`}
                                >
                                  {formatTime(message.timestamp)}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        {sendMessageMutation.isPending && (
                          <div className="flex justify-start">
                            <div className="flex max-w-[80%]">
                              <div className="mr-3">
                                <Avatar>
                                  <AvatarFallback className="bg-primary text-white">AI</AvatarFallback>
                                </Avatar>
                              </div>
                              <div>
                                <div className="px-4 py-3 rounded-lg bg-neutral-100 text-neutral-800">
                                  <div className="flex space-x-2 items-center">
                                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        <div ref={messagesEndRef} />
                      </div>
                    </ScrollArea>

                    {/* Input area */}
                    <div className="p-4 border-t">
                      <form onSubmit={handleSubmit} className="flex space-x-2">
                        <Textarea
                          value={inputMessage}
                          onChange={(e) => setInputMessage(e.target.value)}
                          placeholder="Ask a question about the IEP..."
                          className="flex-1 resize-none"
                          rows={1}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              if (inputMessage.trim()) {
                                handleSubmit(e);
                              }
                            }
                          }}
                        />
                        <Button 
                          type="submit" 
                          className="bg-primary hover:bg-primary-dark"
                          disabled={!inputMessage.trim() || sendMessageMutation.isPending}
                        >
                          <SendHorizontal className="h-4 w-4" />
                        </Button>
                      </form>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Suggested questions sidebar */}
              <div className="hidden md:block">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center text-primary mb-3">
                      <LightbulbIcon className="h-4 w-4 mr-2" />
                      <h3 className="font-medium">Suggested Questions</h3>
                    </div>
                    <div className="space-y-2">
                      {isLoadingSuggestions ? (
                        <>
                          <Skeleton className="h-10 w-full" />
                          <Skeleton className="h-10 w-full" />
                          <Skeleton className="h-10 w-full" />
                          <Skeleton className="h-10 w-full" />
                        </>
                      ) : suggestedQuestions && suggestedQuestions.length > 0 ? (
                        suggestedQuestions.map((question, index) => (
                          <Button 
                            key={index} 
                            variant="outline" 
                            className="w-full justify-start text-left h-auto py-2 text-sm"
                            onClick={() => {
                              setInputMessage(question);
                            }}
                          >
                            {question}
                          </Button>
                        ))
                      ) : (
                        <p className="text-neutral-600 text-sm">
                          {selectedDocument || documentId 
                            ? "No suggested questions for this document." 
                            : "Select a specific document to see suggested questions."}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <h2 className="text-xl font-medium text-neutral-900">Child not found</h2>
            <p className="mt-2 text-neutral-600">The requested child profile does not exist or you don't have access.</p>
            <Button 
              className="mt-4 bg-primary hover:bg-primary-dark"
              asChild
            >
              <Link href="/dashboard">
                Back to Dashboard
              </Link>
            </Button>
          </div>
        )}
      </main>
      <Footer />
    </>
  );
}