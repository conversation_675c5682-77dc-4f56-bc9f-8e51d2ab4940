import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, MessageSquare, User } from "lucide-react";

// usage example: 
// import ContactForm from "@/components/ui/contact-form";
// <ContactForm title="Get in Touch" subtitle="Have questions? We'd love to hear from you." />

// Declare grecaptcha for TypeScript
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}

interface ContactFormProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

export default function ContactForm({ 
  title = "Get in Touch", 
  subtitle = "Have questions? We'd love to hear from you.",
  className = ""
}: ContactFormProps) {
  // Form state
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  // reCAPTCHA state
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [recaptchaError, setRecaptchaError] = useState("");
  const [recaptchaLoaded, setRecaptchaLoaded] = useState(false);
  
  // Get site key from environment variable (Vite uses import.meta.env)
  const recaptchaSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY || "";

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Load reCAPTCHA v3 script
  useEffect(() => {
    if (!recaptchaSiteKey) {
      console.warn('VITE_RECAPTCHA_SITE_KEY not found in environment variables');
      return;
    }

    // Check if script is already loaded
    if (window.grecaptcha) {
      setRecaptchaLoaded(true);
      return;
    }

    // Check if script is already in the document
    const existingScript = document.querySelector(`script[src*="recaptcha"]`);
    if (existingScript) {
      // Wait for it to load
      existingScript.addEventListener('load', () => {
        if (window.grecaptcha) {
          window.grecaptcha.ready(() => {
            setRecaptchaLoaded(true);
          });
        }
      });
      return;
    }

    // Load reCAPTCHA script
    const script = document.createElement('script');
    script.src = `https://www.google.com/recaptcha/api.js?render=${recaptchaSiteKey}`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      console.log('reCAPTCHA script loaded');
      if (window.grecaptcha) {
        window.grecaptcha.ready(() => {
          console.log('reCAPTCHA ready');
          setRecaptchaLoaded(true);
        });
      }
    };
    
    script.onerror = () => {
      console.error('Failed to load reCAPTCHA script');
      setRecaptchaError("Failed to load security verification");
    };

    document.head.appendChild(script);
  }, [recaptchaSiteKey]);

  // Execute reCAPTCHA v3
  const executeRecaptcha = async (): Promise<string | null> => {
    if (!window.grecaptcha || !recaptchaLoaded) {
      setRecaptchaError("Security verification not ready. Please try again.");
      return null;
    }

    try {
      console.log('Executing reCAPTCHA...');
      const token = await window.grecaptcha.execute(recaptchaSiteKey, { 
        action: 'contact' 
      });
      console.log('reCAPTCHA token generated');
      setRecaptchaError("");
      return token;
    } catch (error) {
      console.error('reCAPTCHA execution failed:', error);
      setRecaptchaError("Security verification failed. Please try again.");
      return null;
    }
  };

  // Reset reCAPTCHA when form is reset
  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      subject: "",
      message: ""
    });
    setError("");
    setRecaptchaError("");
    setRecaptchaToken(null);
  };

  // Reset reCAPTCHA when success state changes back to false
  useEffect(() => {
    if (!success) {
      setRecaptchaToken(null);
      setRecaptchaError("");
    }
  }, [success]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess(false);
    setRecaptchaError("");

    // Execute reCAPTCHA v3
    const token = await executeRecaptcha();
    if (!token) {
      setIsLoading(false);
      return; // Error message already set by executeRecaptcha
    }

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          recaptchaToken: token,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        // Reset form
        resetForm();
      } else {
        // Handle different types of errors
        if (data.code === 'RECAPTCHA_VERIFICATION_FAILED') {
          setRecaptchaError(data.message || "Security verification failed");
        } else {
          setError(data.message || "Failed to send message");
        }
      }
    } catch (error) {
      console.error("Contact form error:", error);
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendAnother = () => {
    setSuccess(false);
    resetForm();
  };

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-neutral-900 flex items-center justify-center gap-2">
          <MessageSquare className="h-6 w-6 text-primary" />
          {title}
        </CardTitle>
        {subtitle && (
          <p className="text-neutral-600 mt-2">{subtitle}</p>
        )}
      </CardHeader>
      <CardContent>
        {success ? (
          <div className="text-center space-y-4">
            <div className="p-4 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
              Thank you for your message! We'll get back to you within 24 hours.
            </div>
            <Button 
              variant="outline" 
              onClick={handleSendAnother}
              className="w-full"
            >
              Send Another Message
            </Button>
          </div>
        ) : (
          <>
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md mb-4">
                {error}
              </div>
            )}

            {recaptchaError && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md mb-4">
                {recaptchaError}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Full Name
                  </Label>
                  <Input 
                    id="name"
                    name="name"
                    type="text" 
                    placeholder="Your full name" 
                    className="mt-1"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email Address
                  </Label>
                  <Input 
                    id="email"
                    name="email"
                    type="email" 
                    placeholder="<EMAIL>" 
                    className="mt-1"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="subject">Subject</Label>
                <Input 
                  id="subject"
                  name="subject"
                  type="text" 
                  placeholder="What can we help you with?" 
                  className="mt-1"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                />
              </div>

              <div>
                <Label htmlFor="message">Message</Label>
                <Textarea 
                  id="message"
                  name="message"
                  placeholder="Tell us more about your question or how we can help..." 
                  className="mt-1 min-h-[120px]"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                />
              </div>

              {/* Show warning if reCAPTCHA site key is not configured */}
              {!recaptchaSiteKey && (
                <div className="p-3 text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md">
                  reCAPTCHA not configured. Please set VITE_RECAPTCHA_SITE_KEY environment variable.
                </div>
              )}

              {/* Show reCAPTCHA status */}
              {recaptchaSiteKey && (
                <div className="text-sm text-neutral-500 text-center">
                  <span className={recaptchaLoaded ? "text-green-600" : "text-amber-600"}>
                    {recaptchaLoaded ? "✓ Security verification ready" : "⏳ Loading security verification..."}
                  </span>
                  <div className="flex items-center justify-center mt-1">
                    <span className="text-xs text-neutral-400">
                      Protected by reCAPTCHA v3
                    </span>
                  </div>
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full bg-primary hover:bg-primary-dark"
                disabled={isLoading || (recaptchaSiteKey && !recaptchaLoaded)}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending Message...
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-2" />
                    Send Message
                  </>
                )}
              </Button>
            </form>

            <div className="mt-6 p-4 bg-neutral-50 rounded-lg">
              <p className="text-sm text-neutral-600 text-center">
                <strong>Need immediate help?</strong> You can also reach us at{" "}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-primary hover:text-primary-dark"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}