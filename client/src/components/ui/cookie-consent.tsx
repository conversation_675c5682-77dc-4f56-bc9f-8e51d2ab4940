import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";

// Add type declaration for gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

export default function CookieConsent(): JSX.Element {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem("cookie-consent");
    if (!consent) {
      setShowBanner(true);
    }
  }, []);

  const clearNonEssentialCookies = () => {
    // Clear sidebar state
    localStorage.removeItem("sidebar_state");
    
    // Clear any other non-essential cookies/storage here
    const nonEssentialCookies = ['_ga', '_gid', '_gat']; // Google Analytics cookies
    nonEssentialCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=.${window.location.hostname}`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
    });
  };

  const handleAccept = () => {
    localStorage.setItem("cookie-consent", "accepted");
    setShowBanner(false);
    // Initialize GA when cookies are accepted
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    }
  };

  const handleDecline = () => {
    localStorage.setItem("cookie-consent", "declined");
    setShowBanner(false);
    
    // Disable GA
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }

    // Clear any existing non-essential cookies
    clearNonEssentialCookies();
  };

  const handleLearnMore = () => {
    // Scroll to cookie section if we're already on privacy policy page
    if (window.location.pathname === '/privacy-policy') {
      const cookieSection = document.getElementById('cookies');
      if (cookieSection) {
        cookieSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  if (!showBanner) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-neutral-200 p-4 shadow-lg z-50">
      <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-neutral-600">
          <p>
            We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.{" "}
            <Link 
              href="/privacy-policy#cookies" 
              className="text-primary hover:text-primary-dark underline"
              onClick={handleLearnMore}
            >
              Learn more
            </Link>
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleDecline}
            className="text-sm"
          >
            Decline Non-Essential Cookies
          </Button>
          <Button
            onClick={handleAccept}
            className="text-sm bg-primary hover:bg-primary-dark"
          >
            Accept All Cookies
          </Button>
        </div>
      </div>
    </div>
  );
} 