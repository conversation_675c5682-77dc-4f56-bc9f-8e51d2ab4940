import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface DeleteChildDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  childName: string;
}

export function DeleteChildDialog({
  isOpen,
  onClose,
  onConfirm,
  childName,
}: DeleteChildDialogProps) {
  const [confirmationText, setConfirmationText] = useState("");

  const handleConfirm = () => {
    if (confirmationText === childName) {
      onConfirm();
      onClose();
      setConfirmationText("");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Child Profile</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete:
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>All conversations and messages</li>
              <li>All documents and files</li>
              <li>All insights and analysis</li>
              <li>The child's profile</li>
            </ul>
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <Label htmlFor="confirmation">
            Please type <span className="font-semibold">{childName}</span> to confirm
          </Label>
          <Input
            id="confirmation"
            value={confirmationText}
            onChange={(e) => setConfirmationText(e.target.value)}
            placeholder="Type child's name to confirm"
            className="mt-2"
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={confirmationText !== childName}
          >
            Delete Profile
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 