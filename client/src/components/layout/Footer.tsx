import { Link } from "wouter";

export default function Footer() {
  return (
    <footer className="bg-neutral-900 text-neutral-400 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="flex items-center">
              <img 
                src="/images/ieps-ai-logo.png" 
                alt="IEPs.ai Logo" 
                className="h-12 w-auto"
              />
            </Link>
            <p className="mt-2">
              Empowering parents to navigate IEPs with confidence and clarity.
            </p>
            <p className="mt-4">
              A{" "}
              <a href="https://myparently.com/" target="_blank" rel="noopener noreferrer">
                <strong>
                  <span style={{color: "#f6a71c"}}>my</span>
                  <span style={{color: "#4991e3"}}>Parently</span>
                </strong>
              </a>{" "}
              company.
            </p>
            <div className="mt-4 flex space-x-4">
              <a href="https://www.linkedin.com/company/ieps-ai" target="_blank" rel="noopener noreferrer" className="text-neutral-400 hover:text-white">
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-9h3v9zm-1.5-10.28c-.97 0-1.75-.79-1.75-1.75s.78-1.75 1.75-1.75 1.75.79 1.75 1.75-.78 1.75-1.75 1.75zm15.5 10.28h-3v-4.5c0-1.08-.02-2.47-1.5-2.47-1.5 0-1.73 1.17-1.73 2.39v4.58h-3v-9h2.89v1.23h.04c.4-.75 1.38-1.54 2.84-1.54 3.04 0 3.6 2 3.6 4.59v4.72z" />
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Product</h3>
            <ul className="space-y-2">
              <li>
                <a href="#benefits" className="hover:text-white">
                  Features
                </a>
              </li>
              <li>
                <Link href="/roadmap" className="hover:text-white">
                  Roadmap
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              {/* <li>
                <a href="#" className="hover:text-white">
                  Help Center
                </a>
              </li> */}
              <li>
                <Link href="/contact" className="hover:text-white">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/beta" className="hover:text-white">
                  Beta
                </Link>
              </li>
              {/* <li>
                <a href="#" className="hover:text-white">
                  IEP Resources
                </a>
              </li> 
              <li>
                <a href="#" className="hover:text-white">
                  Community
                </a>
              </li>*/}
            </ul>
          </div>

          {/* <div>
            <h3 className="text-white font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="hover:text-white">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white">
                  Our Story
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white">
                  Careers
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white">
                  Legal
                </a>
              </li>
            </ul>
          </div> */}
        </div>

        <div className="mt-12 pt-8 border-t border-neutral-800 text-sm text-center">
          <p>&copy; {new Date().getFullYear()} IEPs.ai. All rights reserved.</p>
          <div className="mt-2 flex justify-center space-x-6">
            <Link href="/privacy-policy" className="hover:text-white">
              Privacy Policy
            </Link>
            <Link href="/terms-of-use" className="hover:text-white">
              Terms of Service
            </Link>
            <Link href="/privacy-policy#cookies" className="hover:text-white">
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
