import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";

export default function Navbar() {
  const { user, isAuthenticated } = useAuth();
  const [location] = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isHomePage = location === "/";
  
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <nav className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <a href="/" className="flex items-center">
              <img 
              src="/images/ieps-ai-logo.png" 
              alt="IEPs.ai Logo" 
              className="h-12 w-auto"
              />
            </a>
          </div>
          
          <div className="hidden md:flex md:items-center md:space-x-6">
            {isHomePage ? (
              <>
                <a href="#how-it-works" className="text-neutral-600 hover:text-primary font-medium">How It Works</a>
                <a href="#benefits" className="text-neutral-600 hover:text-primary font-medium">Benefits</a>
                <a href="#testimonials" className="text-neutral-600 hover:text-primary font-medium">Testimonials</a>
                <a href="#faq" className="text-neutral-600 hover:text-primary font-medium">FAQ</a>
              </>
            ) : null}
            
            {isAuthenticated && (
              <Link href="/dashboard" className="text-neutral-600 hover:text-primary font-medium">
                My Dashboard
              </Link>
            )}
            
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <div className="relative group">
                  <button className="flex items-center justify-center h-8 w-8 rounded-full bg-neutral-100 overflow-hidden hover:ring-2 hover:ring-primary hover:ring-offset-1 transition-all">
                    {user?.profileImageUrl ? (
                      <img src={user.profileImageUrl} alt="Profile" className="h-8 w-8 object-cover" />
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    )}
                  </button>
                  <div className="absolute right-0 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block min-w-[200px]">

                    <Link href="/coming-soon" className="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">
                      My Profile
                    </Link>
                    <Link href="/coming-soon" className="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">
                      Settings
                    </Link>
                    <div className="border-t border-neutral-200 my-1"></div>
                    <a href="/api/auth/logout" className="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">
                      Log Out
                    </a>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <Button variant="ghost" className="text-primary hover:text-primary-dark font-semibold px-4 py-2" onClick={() => window.location.href = '/login'}>
                  Log In
                </Button>
                <Button className="bg-primary text-white hover:bg-primary-dark rounded-md px-4 py-2 font-semibold shadow-sm transition-all" onClick={() => window.location.href = '/signup'}>
                  Sign Up
                </Button>
              </>
            )}
          </div>
          
          <div className="flex items-center md:hidden">
            <button onClick={toggleMobileMenu} className="text-neutral-600 hover:text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {isHomePage ? (
              <>
                <a href="#how-it-works" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                  How It Works
                </a>
                <a href="#benefits" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                  Benefits
                </a>
                <a href="#testimonials" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                  Testimonials
                </a>
                <a href="#faq" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                  FAQ
                </a>
              </>
            ) : null}
            
            {isAuthenticated && (
              <Link href="/dashboard" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                My Dashboard
              </Link>
            )}
            
            {isAuthenticated ? (
              <>
                <div className="pt-2 pb-1">
                  <div className="px-3 flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      {user?.profileImageUrl ? (
                        <img 
                          className="h-10 w-10 rounded-full object-cover" 
                          src={user.profileImageUrl} 
                          alt="Profile"
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-neutral-100 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="ml-3">
                      <div className="text-base font-medium text-neutral-800">
                        {user?.firstName || 'User'}
                      </div>
                      <div className="text-sm font-medium text-neutral-500">
                        {user?.email || ''}
                      </div>
                    </div>
                  </div>
                </div>
                <Link href="/profile" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                  My Profile
                </Link>
                <Link href="/settings" className="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary hover:bg-neutral-50" onClick={() => setIsMobileMenuOpen(false)}>
                  Settings
                </Link>
                <div className="border-t border-neutral-200 my-1"></div>
                <a href="/api/auth/logout" className="block px-3 py-2 rounded-md text-base font-medium text-primary hover:text-primary-dark hover:bg-neutral-50">
                  Log Out
                </a>
              </>
            ) : (
              <>
                <button 
                  onClick={() => { window.location.href = '/login'; setIsMobileMenuOpen(false); }} 
                  className="w-full text-left block px-3 py-2 rounded-md text-base font-medium text-primary hover:text-primary-dark hover:bg-neutral-50"
                >
                  Log In
                </button>
                <button 
                  onClick={() => { window.location.href = '/signup'; setIsMobileMenuOpen(false); }} 
                  className="w-full text-left block px-3 py-2 rounded-md text-base font-medium bg-primary text-white hover:bg-primary-dark rounded-md"
                >
                  Sign Up
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}
