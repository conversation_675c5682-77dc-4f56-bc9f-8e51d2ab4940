export default function PersonalStory() {
  return (
    <section className="py-12 bg-neutral-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:grid lg:grid-cols-2 lg:gap-8 items-center">
          {/* A close-up of a parent helping a child with schoolwork, showing empathy and connection */}
          <div className="mt-10 lg:mt-0 lg:col-start-1">
            <img
              className="mx-auto lg:mx-0 rounded-lg shadow-md h-64 w-full object-cover md:h-96"
              src="/images/personal-story-parent.png"
              alt="Parent helping child with schoolwork"
            />
          </div>
          <div className="mt-10 lg:mt-0 lg:col-start-2">
            <div className="text-center lg:text-left">
              <h2 className="text-base text-primary font-semibold tracking-wide uppercase">
                Our Story
              </h2>
              <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-neutral-900 sm:text-4xl">
                Why we created IEPs.ai
              </p>
              <div className="mt-6 text-neutral-600">
                <p className="leading-relaxed">
                  When our child was first diagnosed, we found ourselves
                  drowning in paperwork and complex terminology. Every IEP
                  meeting felt overwhelming—like speaking a language we hadn't
                  learned.
                </p>
                <p className="mt-4 leading-relaxed">
                  After countless late nights researching and speaking with
                  other parents and spending $200/hr on advocates to help us, we
                  discovered a universal truth: parents everywhere were
                  struggling to understand what was in their children's IEPs and
                  whether they were getting what they truly needed.
                </p>
                <p className="mt-4 leading-relaxed">
                  That's why we created IEPs.ai—to give parents like us the
                  knowledge, confidence, and clarity we deserve when advocating
                  for our children's education.
                </p>
                <div className="mt-6">
                  <p className="text-primary font-semibold">
                    — Vatché Chamlian, Founder
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
