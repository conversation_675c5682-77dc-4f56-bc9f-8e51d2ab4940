export default function NotAlone() {
  return (
    <section className="py-12 bg-primary bg-opacity-5">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            You're Not Alone
          </h2>
          <p className="mt-4 max-w-2xl text-xl text-white mx-auto">
            Millions of families navigate the IEP process every year, facing similar challenges.
          </p>
        </div>

        <div className="mt-10 max-w-5xl mx-auto">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {/* Stat 1 */}
            <div className="bg-white rounded-lg shadow-soft px-6 py-8 text-center">
              <h3 className="text-5xl font-bold text-primary">7.3M</h3>
              <p className="mt-2 text-neutral-600">Students have IEPs in the U.S.</p>
            </div>
            
            {/* Stat 2 */}
            <div className="bg-white rounded-lg shadow-soft px-6 py-8 text-center">
              <h3 className="text-5xl font-bold text-primary">83%</h3>
              <p className="mt-2 text-neutral-600">Of parents find IEPs complex and overwhelming</p>
            </div>
            
            {/* Stat 3 */}
            <div className="bg-white rounded-lg shadow-soft px-6 py-8 text-center">
              <h3 className="text-5xl font-bold text-primary">12+</h3>
              <p className="mt-2 text-neutral-600">Hours spent by parents preparing for each IEP meeting</p>
            </div>
          </div>
        </div>

        <div className="mt-10 text-center">
          <p className="text-lg italic text-white max-w-3xl mx-auto">
            "Before each IEP meeting, I would stay up all night trying to understand the documents. I knew I was missing important details that could help my son."
          </p>
          <p className="mt-2 text-white opacity-75">— Parent of a 3rd grader with an IEP</p>
        </div>
      </div>
    </section>
  );
}
