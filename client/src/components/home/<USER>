export default function Testimonials() {
  const testimonials = [
    {
      initials: "<PERSON><PERSON>",
      name: "<PERSON>",
      role: "Parent of a 5th grader",
      quote: "IEPs.ai helped me understand what my son's goals actually meant and what progress should look like. I went into our last meeting feeling prepared and confident for the first time.",
      bgColor: "bg-primary"
    },
    {
      initials: "<PERSON><PERSON>",
      name: "<PERSON>",
      role: "Parent of twins with IEPs",
      quote: "Managing two different IEPs was overwhelming until I found IEPs.ai. Now my wife and I can both stay on top of our twins' unique needs and ensure they're getting the support they deserve.",
      bgColor: "bg-primary"
    },
    {
      initials: "<PERSON><PERSON>",
      name: "<PERSON><PERSON>",
      role: "Parent of a 2nd grader",
      quote: "The AI assistant helped me identify accommodations my daughter wasn't receiving. I was able to discuss this with her teacher, and now she's getting the support spelled out in her IEP.",
      bgColor: "bg-primary"
    }
  ];

  return (
    <section id="testimonials" className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-neutral-900 sm:text-4xl">What Parents Are Saying</h2>
          <p className="mt-4 max-w-2xl text-xl text-neutral-600 mx-auto">
            Parents like you are finding clarity and confidence with IEPs.ai.
          </p>
        </div>
        
        <div className="mt-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-neutral-50 rounded-lg p-6 shadow-soft">
                <div className="flex items-center mb-4">
                  <div className={`h-10 w-10 rounded-full ${testimonial.bgColor} text-secondary flex items-center justify-center font-medium`}>
                    {testimonial.initials}
                  </div>
                  <div className="ml-3">
                    <h4 className="text-lg font-semibold text-neutral-900">{testimonial.name}</h4>
                    <p className="text-neutral-600">{testimonial.role}</p>
                  </div>
                </div>
                <div className="text-neutral-700">
                  <p className="italic">{testimonial.quote}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
