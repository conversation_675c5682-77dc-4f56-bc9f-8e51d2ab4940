import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import { Link } from "wouter";

interface FAQItemProps {
  question: string;
  answer: React.ReactNode;
}

function FAQItem({ question, answer }: FAQItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="py-6">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-left w-full flex justify-between items-start text-neutral-900"
      >
        <span className="text-lg font-semibold">{question}</span>
        <span className="ml-6 h-7 flex items-center">
          <ChevronDown
            className={`h-6 w-6 transform ${isOpen ? "rotate-180" : "rotate-0"} transition-transform duration-200`}
          />
        </span>
      </button>
      {isOpen && (
        <div className="mt-2 text-neutral-600">
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function FAQ() {
  const faqItems = [
    {
      question: "How is this different than using chatGPT or something?",
      answer:
        "We have spent the last year building a custom AI model that is trained specifically on IEP documents, feedback from parents, teachers, advocates, as well as, IDEA and FERPA standards. This allows us to provide more accurate and relevant insights than a general purpose AI like chatGPT. If you want to use a general purpose AI, send us a note and we can help you with a custom prompt so you get the most out of it. No Strings attached.",
    },
    {
      question: "Is my child's information secure?",
      answer: (
        <>
          Absolutely. Security is our top priority. All documents are encrypted both in transit and at rest. We will have the ability to automatically anonymize personal information. Please refer to our{" "}
          <Link href="/roadmap" className="text-primary hover:text-primary-dark">
            roadmap
          </Link>{" "}
          for more information.
        </>
      ),
    },
    {
      question: "How does the AI analyze my child's IEP?",
      answer:
        "Our AI is trained specifically on IEP documents and educational terminology. It extracts goals, accommodations, services, and progress measures, then organizes this information into clear, understandable summaries. You can ask questions in natural language, and the AI will reference specific sections of your documents to provide answers.",
    },
    {
      question: "Can I share access with my spouse or partner?",
      answer:
        "Yes! IEPs.ai is designed for family collaboration. You can invite your spouse, partner, or co-parent to your family workspace with full access to all documents and insights. This ensures everyone involved in your child's education can stay informed and prepared.",
    },
    {
      question: "What file types can I upload?",
      answer:
        "IEPs.ai supports PDF, Word documents (.docx), scanned images (.jpg, .png), and Google Docs (via export). Our system can process both digital documents and scanned paper documents with text recognition technology.",
    },
    {
      question: "Can I manage multiple children's IEPs?",
      answer:
        "Absolutely. You can add multiple children to your family workspace, each with their own profile and document library. This makes it easy to manage different IEPs and track progress for each child separately while keeping everything organized in one place.",
    },
  ];

  return (
    <section id="faq" className="py-12 bg-neutral-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-neutral-900 sm:text-4xl">
            Frequently Asked Questions
          </h2>
          <p className="mt-4 max-w-2xl text-xl text-neutral-600 mx-auto">
            Everything you need to know about using IEPs.ai.
          </p>
        </div>

        <div className="mt-12">
          <div className="max-w-3xl mx-auto divide-y-2 divide-neutral-200">
            {faqItems.map((item, index) => (
              <FAQItem
                key={index}
                question={item.question}
                answer={item.answer}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
