import { But<PERSON> } from "@/components/ui/button";
import { useLocation } from "wouter";

export default function CallToAction() {
  const [, navigate] = useLocation();

  return (
    <section className="py-12 bg-primary-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
        <h2 className="text-3xl font-extrabold sm:text-4xl">
          Take control of your child's education journey
        </h2>
        <p className="mt-4 text-xl max-w-2xl mx-auto opacity-90">
          Join thousands of parents who've found clarity and confidence in navigating their children's IEPs.
        </p>
        <div className="mt-8">
          <Button 
            onClick={() => navigate('/signup')}
            className="bg-white text-primary font-bold rounded-md px-6 py-3 text-lg shadow-md hover:bg-neutral-100 transition-all"
          >
            Get Started — It's Free
          </Button>
          <p className="mt-4 text-sm opacity-75">No credit card required. Start making sense of your child's IEP today.</p>
        </div>
      </div>
    </section>
  );
}
