import React from "react";
import { File, Lightbulb, MessageSquare, Users } from "lucide-react";

export default function Benefits() {
  const benefits = [
    {
      iconName: "File",
      title: "Simple Organization",
      description: "Store all your IEP documents in one secure place, organized by child, year, and type."
    },
    {
      iconName: "Lightbulb",
      title: "AI-Powered Insights",
      description: "Our AI analyzes IEP documents to highlight key information and explain complex terminology."
    },
    {
      iconName: "MessageSquare",
      title: "Personalized Guidance",
      description: "Ask questions about your child's IEP and receive clear, actionable answers from our AI assistant."
    },
    {
      iconName: "Users",
      title: "Family Collaboration",
      description: "Share access with your partner or spouse to ensure everyone is on the same page for your child."
    }
  ];

  return (
    <section id="benefits" className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-neutral-900 sm:text-4xl">How IEPs.ai Helps</h2>
          <p className="mt-4 max-w-2xl text-xl text-neutral-600 mx-auto">
            Transform overwhelming IEP documents into actionable insights with our AI-powered platform.
          </p>
        </div>

        <div className="mt-12">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-neutral-50 rounded-lg p-6 shadow-soft text-center">
                <div className="h-12 w-12 mx-auto mb-4 flex items-center justify-center rounded-full bg-primary">
                  {benefit.iconName === "File" && <File className="h-6 w-6 text-white" />}
                  {benefit.iconName === "Lightbulb" && <Lightbulb className="h-6 w-6 text-white" />}
                  {benefit.iconName === "MessageSquare" && <MessageSquare className="h-6 w-6 text-white" />}
                  {benefit.iconName === "Users" && <Users className="h-6 w-6 text-white" />}
                </div>
                <h3 className="text-lg font-semibold text-neutral-900">{benefit.title}</h3>
                <p className="mt-2 text-neutral-600">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
