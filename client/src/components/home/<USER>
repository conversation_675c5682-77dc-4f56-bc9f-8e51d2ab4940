export default function HowItWorks() {
  const steps = [
    {
      number: 1,
      title: "Upload your IEP documents",
      description: "Securely upload your child's IEP and related documents. We automatically organize them by type and date.",
      image: "https://images.unsplash.com/photo-1586282391129-76a6df230234?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
      alt: "Person uploading documents"
    },
    {
      number: 2,
      title: "AI analyzes the documents",
      description: "Our AI reads and analyzes your documents, extracting goals, accommodations, and key information.",
      image: "https://images.unsplash.com/photo-1599658880436-c61792e70672?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
      alt: "AI analyzing documents"
    },
    {
      number: 3,
      title: "Get insights and ask questions",
      description: "Review AI-generated insights and ask specific questions about your child's IEP in simple language.",
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80",
      alt: "Parent reviewing insights on tablet"
    }
  ];

  return (
    <section id="how-it-works" className="py-12 bg-neutral-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-neutral-900 sm:text-4xl">How It Works</h2>
          <p className="mt-4 max-w-2xl text-xl text-neutral-600 mx-auto">
            IEPs.ai makes navigating your child's education simple in just a few steps.
          </p>
        </div>
        
        <div className="mt-16">
          <div className="lg:grid lg:grid-cols-3 lg:gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative mt-10 lg:mt-0 first:mt-0">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white mb-4 font-bold text-lg">
                  {step.number}
                </div>
                <h3 className="text-xl font-semibold text-neutral-900">{step.title}</h3>
                <p className="mt-2 text-neutral-600">
                  {step.description}
                </p>
                <img
                  src={
                    index === 0 ? '/images/howitworks-upload.png' :
                    index === 1 ? '/images/howitworks-ai.jpg' :
                    '/images/howitworks-insights.jpg'
                  }
                  alt={step.alt}
                  className="mt-4 rounded-lg shadow-md h-48 w-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
