import { useState } from "react";
import { MessageCircle, Send, X, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";

interface FeedbackWidgetProps {
  page?: string; // Optional page identifier, will auto-detect if not provided
}

export default function FeedbackWidget({ page }: FeedbackWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Auto-detect current page if not provided
  const getCurrentPage = () => {
    if (page) return page;
    
    const path = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    
    if (path === '/') return 'Home';
    if (path === '/dashboard') return 'Dashboard';
    if (path === '/roadmap') return 'Roadmap';
    if (path === '/pricing') return 'Pricing';
    if (path === '/terms-of-use') return 'Terms of Use';
    if (path === '/privacy-policy') return 'Privacy Policy';
    if (path.startsWith('/conversation/')) {
      const conversationId = searchParams.get('conversation');
      return `Conversation${conversationId ? ` (ID: ${conversationId})` : ''}`;
    }
    if (path.startsWith('/child-profile/')) return 'Child Profile';
    if (path.startsWith('/document-viewer')) return 'Document Viewer';
    if (path.startsWith('/document-upload/')) return 'Document Upload';
    if (path === '/join-family') return 'Join Family';
    if (path === '/reset-password') return 'Reset Password';
    if (path === '/ai-assistant') return 'AI Assistant';
    
    return path; // Fallback to the raw path
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!feedback.trim()) {
      toast({
        title: "Message required",
        description: "Please enter your feedback before submitting.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          message: feedback,
          page: getCurrentPage(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }

      toast({
        title: "Feedback sent!",
        description: "Thank you for your feedback. We appreciate your input!"
      });

      setFeedback("");
      setIsOpen(false);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: "Error",
        description: "Failed to send feedback. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Floating Feedback Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-200 bg-primary hover:bg-primary-dark"
          size="icon"
        >
          <MessageCircle className="h-6 w-6" />
          <span className="sr-only">Send Feedback</span>
        </Button>
      </div>

      {/* Feedback Modal */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Send Feedback
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="feedback-message">Your feedback</Label>
              <Textarea
                id="feedback-message"
                placeholder="Tell us what you think! Report bugs, suggest features, or share your experience..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="mt-1 min-h-[100px] resize-none"
                disabled={isSubmitting}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                className="flex-1"
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1"
                disabled={isSubmitting || !feedback.trim()}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send
                  </>
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}