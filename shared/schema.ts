import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  uuid,
  boolean,
  integer
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  // Added for email/password authentication
  passwordHash: varchar("password_hash"), // For users who sign up with email/password
  emailVerified: boolean("email_verified").default(false), // For email verification
  authProvider: varchar("auth_provider").default("email"), // 'email', 'google', 'facebook', etc.
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;

// Children table - for storing information about children with IEPs
export const children = pgTable("children", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: varchar("name").notNull(),
  birthMonth: varchar("birth_month"),
  birthYear: varchar("birth_year"),
  avatarUrl: varchar("avatar_url"),
  createdBy: varchar("created_by").notNull().references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertChildSchema = createInsertSchema(children);
export type InsertChild = typeof children.$inferInsert;
export type Child = typeof children.$inferSelect;

// Documents table - for storing IEP documents
export const documents = pgTable("documents", {
  id: uuid("id").defaultRandom().primaryKey(),
  childId: uuid("child_id").notNull().references(() => children.id),
  title: varchar("title").notNull(),
  documentType: varchar("document_type").notNull(), // IEP, evaluation, progress report, etc.
  schoolYear: varchar("school_year"), // e.g., "2023-2024"
  fileText: text("file_text"), // The actual document text, for simplicity in this prototype
  fileUrl: varchar("file_url"), // URL to access the file
  uploadedBy: varchar("uploaded_by").notNull().references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertDocumentSchema = createInsertSchema(documents);
export type InsertDocument = typeof documents.$inferInsert;
export type Document = typeof documents.$inferSelect;

// Family members table - for parent/spouse sharing
export const familyMembers = pgTable("family_members", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id), // The user who created the sharing
  familyMemberId: varchar("family_member_id").notNull().references(() => users.id), // The user being shared with
  relationship: varchar("relationship"), // spouse, partner, etc.
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertFamilyMemberSchema = createInsertSchema(familyMembers);
export type InsertFamilyMember = typeof familyMembers.$inferInsert;
export type FamilyMember = typeof familyMembers.$inferSelect;

// Family invitations table - for pending invitations
export const familyInvitations = pgTable("family_invitations", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id), // The user sending the invitation
  email: varchar("email").notNull(), // Email of the invited person
  status: varchar("status").notNull(), // pending, accepted, declined
  createdAt: timestamp("created_at").defaultNow(),
  respondedAt: timestamp("responded_at"),
});

export const insertFamilyInvitationSchema = createInsertSchema(familyInvitations);
export type InsertFamilyInvitation = typeof familyInvitations.$inferInsert;
export type FamilyInvitation = typeof familyInvitations.$inferSelect;

// Conversations table - for storing AI conversations
export const conversations = pgTable("conversations", {
  id: uuid("id").defaultRandom().primaryKey(),
  childId: uuid("child_id").notNull().references(() => children.id),
  title: varchar("title"),
  createdBy: varchar("created_by").notNull().references(() => users.id),
  hasSummary: boolean("has_summary").default(false),
  documentCountAtSummary: integer("document_count_at_summary").default(0),
  summary: text("summary"), // Store the AI-generated summary
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertConversationSchema = createInsertSchema(conversations);
export type InsertConversation = typeof conversations.$inferInsert;
export type Conversation = typeof conversations.$inferSelect;

// Messages table - for storing conversation messages
export const messages = pgTable("messages", {
  id: uuid("id").defaultRandom().primaryKey(),
  conversationId: uuid("conversation_id").notNull().references(() => conversations.id),
  role: varchar("role").notNull(), // user or assistant
  content: text("content").notNull(),
  timestamp: timestamp("timestamp").notNull(),
});

export const insertMessageSchema = createInsertSchema(messages);
export type InsertMessage = typeof messages.$inferInsert;
export type Message = typeof messages.$inferSelect;